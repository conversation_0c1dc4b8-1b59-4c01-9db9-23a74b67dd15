2025-08-22 10:40:09.125 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.341 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.481 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.622 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.675 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.678 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.680 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.681 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.683 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:28.044 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:43.730 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 11:11:36.706 [AWT-EventQueue-0] ERROR com.uino.x.migration.ui.SqlTransformationPanel - 更新转换引擎失败
java.lang.UnsupportedOperationException: null
	at java.base/java.util.AbstractList.add(AbstractList.java:153)
	at java.base/java.util.AbstractList.add(AbstractList.java:111)
	at java.base/java.util.AbstractCollection.addAll(AbstractCollection.java:336)
	at com.uino.x.migration.ui.SqlTransformationPanel.updateTransformationEngine(SqlTransformationPanel.java:261)
	at com.uino.x.migration.ui.SqlTransformationPanel.initializeTransformationEngine(SqlTransformationPanel.java:238)
	at com.uino.x.migration.ui.SqlTransformationPanel.<init>(SqlTransformationPanel.java:48)
	at com.uino.x.migration.ui.MainFrame.initializeComponents(MainFrame.java:91)
	at com.uino.x.migration.ui.MainFrame.<init>(MainFrame.java:59)
	at com.uino.x.migration.DatabaseMigrationToolApplication.lambda$0(DatabaseMigrationToolApplication.java:39)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:742)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
