2025-08-25 11:19:22.332 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.345 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-25 11:19:22.512 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.516 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-25 11:19:22.637 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.637 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-25 11:19:22.672 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-25 11:19:22.700 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-25 11:19:22.700 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-25 11:19:22.700 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-25 11:19:22.700 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-25 11:19:22.716 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.763 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-25 11:19:22.798 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.799 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-25 11:19:22.799 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-25 11:19:22.800 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.801 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-25 11:19:22.802 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.803 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-25 11:19:22.805 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.806 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-25 11:19:22.807 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.807 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-25 11:19:22.818 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.820 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:22.826 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-25 11:19:24.472 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-25 11:19:34.846 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 40mysql-31达梦
2025-08-25 11:19:34.848 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:34.851 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=sys
2025-08-25 11:19:34.853 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=SYSDBA
2025-08-25 11:19:34.854 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已应用数据库配置: 40mysql-31达梦
2025-08-25 11:19:39.630 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:40.078 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 成功加载 290 个源数据库Schema
2025-08-25 11:19:42.111 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 成功加载源数据库Schema，共290个
2025-08-25 11:19:50.067 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:19:50.067 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.ui.MigrationPanel - 使用源数据库配置: host=***********, port=3307, database=aiznyj_modelx, username=root
2025-08-25 11:19:50.121 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 开始导出Schema: aiznyj_modelx (不转换Schema) 到路径: C:\Users\<USER>\Desktop\data-export
2025-08-25 11:19:50.121 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 导出所有表和视图
2025-08-25 11:19:50.122 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 一键迁移过滤表信息: 排除表 [flyway_schema_history] 不进行常规迁移
2025-08-25 11:19:50.133 [SwingWorker-pool-2-thread-2] INFO  c.u.x.common.core.factory.ExecutorServiceFactory - sql-utils executor is create!
2025-08-25 11:19:50.408 [SwingWorker-pool-2-thread-2] INFO  c.u.x.common.core.factory.ExecutorServiceFactory - async-utils executor is create!
2025-08-25 11:19:50.433 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=texture_resource, length=641, columns=10, hasMetadata=true, sql=CREATE TABLE `texture_resource` (
  `texture_id` VARCHAR(100) NOT NULL COMMENT '贴图Id',
  `extend_name` VARCHAR(10) COMMENT '文件扩展名',
  `file_exist` TINYINT COMMENT '资源是否存在(不存在：1；存在：2)',
  `file_size` BIGINT DEFAULT 0 COMMENT '文件大小（单位：字节）',
  `upload_time` DATETIME COMMENT '上传时间',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`texture_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='贴图资源'
2025-08-25 11:19:50.434 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=things_model, length=1017, columns=17, hasMetadata=true, sql=CREATE TABLE `things_model` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '编号',
  `model_id` VARCHAR(100) COMMENT '模型编号',
  `model_type` TINYINT NOT NULL COMMENT '模型分类（基础模型：1；用户模型：2）',
  `type` VARCHAR(30) COMMENT '类型',
  `title` VARCHAR(50) COMMENT '模型名',
  `version` VARCHAR(20) COMMENT '版本',
  `size` VARCHAR(128) COMMENT '尺寸',
  `tags` TEXT COMMENT '标签',
  `dir_flag` TINYINT DEFAULT 1 COMMENT '是否为目录节点(1：默认叶子，2：目录)',
  `file_exist` TINYINT DEFAULT 1 COMMENT '模型是否存在(不存在：1；存在：2)',
  `file_size` BIGINT DEFAULT 0 COMMENT '文件大小（单位：字节）',
  `classify` VARCHAR(200) COMMENT '模型分类全路径(以斜杠进行分隔)',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物模型表';
CREATE UNIQUE INDEX `model_id` ON `things_model` (`model_id`)
2025-08-25 11:19:50.435 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=undo_log, length=712, columns=8, hasMetadata=true, sql=CREATE TABLE `undo_log` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'id',
  `branch_id` BIGINT NOT NULL COMMENT 'branch transaction id',
  `xid` VARCHAR(128) NOT NULL COMMENT 'global transaction id',
  `context` VARCHAR(128) NOT NULL COMMENT 'undo_log context,such as serialization',
  `rollback_info` LONGBLOB NOT NULL COMMENT 'rollback info',
  `log_status` INT NOT NULL COMMENT '0:normal status,1:defense status',
  `log_created` DATETIME NOT NULL COMMENT 'create datetime',
  `log_modified` DATETIME NOT NULL COMMENT 'modify datetime'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AT transaction mode undo table';
CREATE UNIQUE INDEX `ux_undo_log` ON `undo_log` (`xid`, `branch_id`)
2025-08-25 11:19:50.605 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - 开始关闭Schema SQL制造者: aiznyj_modelx
2025-08-25 11:19:50.606 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - Schema SQL制造者关闭完成: aiznyj_modelx
2025-08-25 11:19:50.606 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 生成的DROP语句长度: 40
2025-08-25 11:19:50.606 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 生成的CREATE语句长度: 32
2025-08-25 11:19:50.606 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - 开始关闭Schema SQL制造者: aiznyj_modelx
2025-08-25 11:19:50.606 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - Schema SQL制造者关闭完成: aiznyj_modelx
2025-08-25 11:19:50.606 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - Schema导出完成: C:\Users\<USER>\Desktop\data-export
2025-08-25 11:40:21.567 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:21.569 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-25 11:40:21.717 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:21.722 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-25 11:40:21.870 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:21.872 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-25 11:40:21.925 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-25 11:40:21.969 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-25 11:40:21.970 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-25 11:40:21.970 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-25 11:40:21.970 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-25 11:40:22.023 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.051 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-25 11:40:22.093 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.093 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-25 11:40:22.094 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-25 11:40:22.096 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.097 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-25 11:40:22.100 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.101 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-25 11:40:22.109 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.111 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-25 11:40:22.112 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.115 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.116 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-25 11:40:22.135 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.137 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:22.148 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-25 11:40:22.569 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-25 11:40:53.923 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:55.277 [Thread-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 11:40:55.680 [Thread-0] ERROR com.uino.x.migration.ui.SqlExecutionPanel - 数据库操作失败
java.sql.SQLException: Statement.executeUpdate() or Statement.executeLargeUpdate() cannot issue statements that produce result sets.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:81)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:55)
	at com.mysql.cj.jdbc.StatementImpl.executeUpdateInternal(StatementImpl.java:1413)
	at com.mysql.cj.jdbc.StatementImpl.executeLargeUpdate(StatementImpl.java:2322)
	at com.mysql.cj.jdbc.StatementImpl.executeUpdate(StatementImpl.java:1370)
	at com.alibaba.druid.pool.DruidPooledStatement.executeUpdate(DruidPooledStatement.java:328)
	at com.uino.x.migration.ui.SqlExecutionPanel.executeUpdate(SqlExecutionPanel.java:493)
	at com.uino.x.migration.ui.SqlExecutionPanel.performSqlExecution(SqlExecutionPanel.java:400)
	at com.uino.x.migration.ui.SqlExecutionPanel.lambda$executeSql$7(SqlExecutionPanel.java:346)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-25 11:41:35.681 [Thread-1] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.163 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.200 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.211 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-25 12:26:03.212 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.213 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.213 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-25 12:26:03.214 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-25 12:26:03.214 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-25 12:26:03.215 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.215 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-25 12:26:03.216 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.216 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-25 12:26:03.217 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.217 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-25 12:26:03.218 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.219 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.219 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-25 12:26:03.225 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.226 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.233 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.234 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:03.234 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-25 12:26:03.234 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-25 12:26:12.980 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:12.983 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-25 12:26:13.166 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.170 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-25 12:26:13.305 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.306 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-25 12:26:13.350 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-25 12:26:13.390 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-25 12:26:13.390 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-25 12:26:13.390 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-25 12:26:13.390 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-25 12:26:13.447 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.482 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-25 12:26:13.513 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.514 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-25 12:26:13.514 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-25 12:26:13.516 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.516 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-25 12:26:13.517 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.518 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-25 12:26:13.521 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.522 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-25 12:26:13.524 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.527 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.527 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-25 12:26:13.544 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.546 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:26:13.556 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-25 12:26:13.955 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-25 12:26:41.765 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:28:36.931 [Thread-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-25 12:28:37.384 [Thread-0] ERROR com.uino.x.migration.ui.SqlExecutionPanel - 数据库操作失败
java.sql.SQLSyntaxErrorException: Table 'sys.things_model' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.StatementImpl.executeUpdateInternal(StatementImpl.java:1500)
	at com.mysql.cj.jdbc.StatementImpl.executeLargeUpdate(StatementImpl.java:2322)
	at com.mysql.cj.jdbc.StatementImpl.executeUpdate(StatementImpl.java:1370)
	at com.alibaba.druid.pool.DruidPooledStatement.executeUpdate(DruidPooledStatement.java:328)
	at com.uino.x.migration.ui.SqlExecutionPanel.executeSqlInSchema(SqlExecutionPanel.java:874)
	at com.uino.x.migration.ui.SqlExecutionPanel.performSqlExecution(SqlExecutionPanel.java:529)
	at com.uino.x.migration.ui.SqlExecutionPanel.lambda$executeSql$8(SqlExecutionPanel.java:444)
	at java.base/java.lang.Thread.run(Thread.java:840)
