package com.uino.x.pedestal.tenant.migrate.impl;


import com.uino.x.common.sql.maker.SchemaSql;
import com.uino.x.common.sql.maker.SchemaSqlFactory;
import com.uino.x.common.sql.maker.TableSql;
import com.uino.x.common.sql.maker.ViewSql;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.pedestal.tenant.migrate.AbstractTenantMigration;
import com.uino.x.pedestal.tenant.migrate.TenantMigration;
import com.uino.x.pedestal.tenant.migrate.TenantMigrationInfo;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 所有数据的租户迁移
 *
 * <AUTHOR>
 * @version 0.0.5
 * @date 2021/11/26 10:37
 */
public class AllTenantMigration extends AbstractTenantMigration implements TenantMigration {


    public AllTenantMigration(DataSource dataSource, TenantMigrationInfo tenantMigrationInfo) {
        super(dataSource, tenantMigrationInfo);
    }
    
    /**
     * 智能解析完整的CREATE语句，避免错误分割包含分号的语句
     * 
     * @param sql 包含CREATE语句的SQL字符串
     * @return 完整的CREATE语句列表
     */
    private List<String> parseCompleteCreateStatements(String sql) {
        List<String> statements = new ArrayList<>();
        if (StringUtils.isBlank(sql)) {
            return statements;
        }
        
        // 预处理：移除多余的空白行和连续分号
        String cleanedSql = sql.replaceAll("\\s*;\\s*;+", ";")  // 移除连续分号
                              .replaceAll("\\n\\s*;\\s*\\n", "\\n")  // 移除单独的分号行
                              .replaceAll("\\s*;\\s*$", ";")  // 确保末尾分号格式正确
                              .trim();
        
        StringBuilder currentStatement = new StringBuilder();
        boolean inQuotes = false;
        boolean inComment = false;
        boolean inLineComment = false;
        char quoteChar = '\0';
        
        for (int i = 0; i < cleanedSql.length(); i++) {
            char c = cleanedSql.charAt(i);
            char nextChar = (i + 1 < cleanedSql.length()) ? cleanedSql.charAt(i + 1) : '\0';
            
            // 处理行注释
            if (!inQuotes && !inComment && c == '-' && nextChar == '-') {
                inLineComment = true;
                currentStatement.append(c);
                continue;
            }
            
            // 行注释在换行符处结束
            if (inLineComment && (c == '\n' || c == '\r')) {
                inLineComment = false;
                currentStatement.append(c);
                continue;
            }
            
            // 处理块注释
            if (!inQuotes && !inLineComment && c == '/' && nextChar == '*') {
                inComment = true;
                currentStatement.append(c);
                continue;
            }
            
            if (inComment && c == '*' && nextChar == '/') {
                inComment = false;
                currentStatement.append(c);
                continue;
            }
            
            // 在注释中，直接添加字符
            if (inComment || inLineComment) {
                currentStatement.append(c);
                continue;
            }
            
            // 处理引号
            if (!inQuotes && (c == '\'' || c == '"' || c == '`')) {
                inQuotes = true;
                quoteChar = c;
                currentStatement.append(c);
                continue;
            }
            
            if (inQuotes && c == quoteChar) {
                // 检查是否是转义的引号
                if (i > 0 && cleanedSql.charAt(i - 1) != '\\') {
                    inQuotes = false;
                    quoteChar = '\0';
                }
                currentStatement.append(c);
                continue;
            }
            
            // 在引号中，直接添加字符
            if (inQuotes) {
                currentStatement.append(c);
                continue;
            }
            
            // 处理分号（只有在不在引号和注释中时才作为语句分隔符）
            if (c == ';') {
                String statement = currentStatement.toString().trim();
                
                // 更严格的验证：确保语句不为空且包含有效的SQL关键字
                if (StringUtils.isNotBlank(statement) && 
                    statement.length() > 1) {
                    
                    String upperStatement = statement.toUpperCase();
                    // 验证是否包含有效的SQL关键字
                    if (upperStatement.contains("CREATE") || 
                        upperStatement.contains("DROP") || 
                        upperStatement.contains("INSERT") || 
                        upperStatement.contains("UPDATE") || 
                        upperStatement.contains("DELETE") || 
                        upperStatement.contains("ALTER")) {
                        
                        // 不添加分号，因为jdbcTemplate.batchUpdate会自动添加
                        statements.add(statement);
                        System.out.println("Parsed valid SQL statement: " + statement.substring(0, Math.min(statement.length(), 50)) + "...");
                    } else {
                        System.err.println("Skipping invalid SQL fragment: " + statement);
                    }
                }
                currentStatement = new StringBuilder();
                continue;
            }
            
            currentStatement.append(c);
        }
        
        // 处理最后一个语句（如果没有以分号结尾）
        String lastStatement = currentStatement.toString().trim();
        if (StringUtils.isNotBlank(lastStatement) && 
            !lastStatement.equals(";") && 
            lastStatement.length() > 1) {
            
            String upperLastStatement = lastStatement.toUpperCase();
            // 验证是否包含有效的SQL关键字
            if (upperLastStatement.contains("CREATE") || 
                upperLastStatement.contains("DROP") || 
                upperLastStatement.contains("INSERT") || 
                upperLastStatement.contains("UPDATE") || 
                upperLastStatement.contains("DELETE") || 
                upperLastStatement.contains("ALTER")) {
                
                // 不添加分号，因为jdbcTemplate.batchUpdate会自动添加
                statements.add(lastStatement);
                System.out.println("Parsed final SQL statement: " + lastStatement.substring(0, Math.min(lastStatement.length(), 50)) + "...");
            } else {
                System.err.println("Skipping invalid final SQL fragment: " + lastStatement);
            }
        }
        
        return statements;
    }

    @Override
    public void migrate() {
        final boolean cover = tenantMigrationInfo.isCover();
        try {
            copyTableData(cover);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 复制表数据
     *
     * <AUTHOR>
     * @date 2021/11/26 13:30
     */
//    public void copyTableData(boolean cover) {
//
//        final String sourceSchema = tenantMigrationInfo.getSourceTenant();
//        final String targetSchema = tenantMigrationInfo.getTargetTenant();
//        // 查询封装basex的表数据复制sql
//        final StringBuilder copySqlBuilder = new StringBuilder();
//        String sourceTenantBasexSchema = getTenantSchema(sourceSchema, DataSourceConstant.SYSTEM_X);
//        String targetTenantBasexSchema = getTenantSchema(targetSchema, DataSourceConstant.SYSTEM_X);
//        final List<String> basexTables = queryTableNames(sourceTenantBasexSchema);
//        for (String table : basexTables) {
//            if (cover) {
//
//                buildCopyDataSql(copySqlBuilder, sourceTenantBasexSchema, table, targetTenantBasexSchema, table);
//            } else {
//
//                buildCopyTableSql(copySqlBuilder, sourceTenantBasexSchema, table, targetTenantBasexSchema, table);
//            }
//        }
//        // 查询封装auto_api的表数据复制sql
//        String sourceTenantAutoApiSchema = getTenantSchema(sourceSchema, DataSourceConstant.AUTO_API);
//        String targetTenantAutoApiSchema = getTenantSchema(targetSchema, DataSourceConstant.AUTO_API);
//        final List<String> autoApiTables = queryTableNames(sourceTenantAutoApiSchema);
//        for (String table : autoApiTables) {
//            if (cover) {
//
//                buildCopyDataSql(copySqlBuilder, sourceTenantAutoApiSchema, table, targetTenantAutoApiSchema, table);
//            } else {
//
//                buildCopyTableSql(copySqlBuilder, sourceTenantAutoApiSchema, table, targetTenantAutoApiSchema, table);
//            }
//        }
//
//
//        // 执行sql
//        jdbcTemplate.update(copySqlBuilder.toString());
//    }
    /**
     * 复制表数据
     *
     * <AUTHOR>
     * @date 2021/11/26 13:30
     */
    public void copyTableData(boolean cover) throws SQLException {

        final String sourceSchema = tenantMigrationInfo.getSourceTenant();
        final String targetSchema = tenantMigrationInfo.getTargetTenant();
        String sourceTenantBasexSchema = getTenantSchema(sourceSchema, tenantMigrationInfo.getSourceType());
        String targetTenantBasexSchema = getTenantSchema(targetSchema, tenantMigrationInfo.getSourceType());
        List<String> sqlList = new ArrayList<>();
        // TODO: 2025-03-01 使用SqlUtils导出表结构
        final SchemaSql schemaSql = SchemaSqlFactory.get(dataSource, sourceTenantBasexSchema);
        schemaSql.output().setName(targetTenantBasexSchema);
        for (TableSql tableSql : schemaSql.getTableSqlList()) {
            if (cover) {
                final String drop = tableSql.getDrop();
                if (StringUtils.isNotBlank(drop)) {
                    // 使用智能解析处理DROP语句，确保去除末尾分号
                    List<String> dropStatements = parseCompleteCreateStatements(drop);
                    
                    for (String dropSql : dropStatements) {
                        // parseCompleteCreateStatements已经进行了验证，直接添加
                        sqlList.add(dropSql);
                    }
                }
                final String create = tableSql.getCreate();
                if (StringUtils.isNotBlank(create)) {
                    // 智能解析CREATE语句，避免错误分割包含分号的语句
                    String trimmedCreate = create.trim();
                    
                    // 使用智能SQL解析，不添加分号因为jdbcTemplate.batchUpdate会自动添加
                    List<String> createStatements = parseCompleteCreateStatements(trimmedCreate);
                    
                    // 验证并添加CREATE语句
                    for (String createSql : createStatements) {
                        String upperCaseSql = createSql.trim().toUpperCase();
                        if (upperCaseSql.startsWith("CREATE")) {
                            sqlList.add(createSql);
                            System.out.println("Added CREATE statement: " + createSql.substring(0, Math.min(createSql.length(), 50)) + "...");
                        } else {
                            // 记录可能有问题的SQL语句
                            System.err.println("Warning: Skipping invalid CREATE statement: " + createSql);
                        }
                    }
                }
            }
            buildCopyDataSql(sourceTenantBasexSchema, tableSql.getOriginalName(), targetTenantBasexSchema, tableSql.getOriginalName(), sqlList);
        }
        for (ViewSql viewSql : schemaSql.getViewSqlList()) {
            if (!cover) {
                final String dropView = viewSql.getDrop();
                if (StringUtils.isNotBlank(dropView)) {
                    // 使用智能解析处理DROP VIEW语句
                    List<String> dropStatements = parseCompleteCreateStatements(dropView);
                    
                    for (String dropSql : dropStatements) {
                        // parseCompleteCreateStatements已经进行了验证，直接添加
                        sqlList.add(dropSql);
                    }
                }
            }
            final String createView = viewSql.getCreate();
            if (StringUtils.isNotBlank(createView)) {
                // 使用智能解析处理CREATE VIEW语句
                List<String> createStatements = parseCompleteCreateStatements(createView);
                
                for (String createSql : createStatements) {
                    // parseCompleteCreateStatements已经进行了验证，直接添加
                    sqlList.add(createSql);
                }
            }
        }

//        final List<TableAbstract> basexAbstracts = queryTableNames(sourceTenantBasexSchema);
//        Map<String, List<TableAbstract>> abstracts = basexAbstracts.stream().collect(Collectors.groupingBy(TableAbstract::getTableType));
        // 查询封装basex的表数据复制sql
//        if (Objects.nonNull(abstracts.get("BASE TABLE"))) {
//            for (TableAbstract table : abstracts.get("BASE TABLE")) {
//                if (cover) {
//                    buildCopyDataSql(sourceTenantBasexSchema, table.getTableName(), targetTenantBasexSchema, table.getTableName(), sqlList);
//                } else {
//                    buildCopyTableSql(sourceTenantBasexSchema, table.getTableName(), targetTenantBasexSchema, table.getTableName(), sqlList);
//                    buildCopyDataSql(sourceTenantBasexSchema, table.getTableName(), targetTenantBasexSchema, table.getTableName(), sqlList);
//                }
//            }
//        }
        // 查询封装basex的视图数据复制sql
//        if (Objects.nonNull(abstracts.get("VIEW"))) {
//            for (TableAbstract view : abstracts.get("VIEW")) {
//                String sql = queryViewStruct(view.getTableName(), sourceTenantBasexSchema, targetTenantBasexSchema);
//                if (!cover) {
//                    sqlList.add(String.format("drop view if exists `%s`.`%s`", targetSchema, view.getTableName()));
//                }
//                sqlList.add(sql);
//            }
//        }

        // 在批量执行SQL前添加验证和日志输出
        if (sqlList.isEmpty()) {
            System.out.println("No SQL statements to execute.");
            return;
        }
        
        // 简化的最终验证：只检查基本的空值和格式，不重复添加分号
        List<String> finalSqlList = new ArrayList<>();
        for (int i = 0; i < sqlList.size(); i++) {
            String sql = sqlList.get(i);
            
            // 基本验证：跳过空白或仅分号的语句
            if (StringUtils.isBlank(sql) || sql.trim().equals(";")) {
                System.err.println("Skipping empty SQL at index " + i + ": '" + sql + "'");
                continue;
            }
            
            // 简单清理：去除多余的空白，但不重复添加分号
            // parseCompleteCreateStatements和buildCopyDataSql已经确保了分号的正确性
            String cleanedSql = sql.trim();
            
            finalSqlList.add(cleanedSql);
            System.out.println("Final SQL [" + i + "]: " + cleanedSql + " ");
        }

        if (finalSqlList.isEmpty()) {
            System.err.println("No valid SQL statements found after final validation.");
            return;
        }

        System.out.println("Executing " + finalSqlList.size() + " SQL statements in batch...");
        try {
            jdbcTemplate.batchUpdate(finalSqlList.toArray(new String[0]));
            System.out.println("Successfully executed " + finalSqlList.size() + " SQL statements.");
        } catch (Exception e) {
            System.err.println("Error executing batch update: " + e.getMessage());
            // 输出前几个SQL语句用于调试
            for (int i = 0; i < Math.min(3, finalSqlList.size()); i++) {
                System.err.println("SQL [" + i + "]: " + finalSqlList.get(i));
            }
            e.printStackTrace();
            throw e;
        }
//         执行sql
//        jdbcTemplate.update(copySqlBuilder.toString());
    }
}

