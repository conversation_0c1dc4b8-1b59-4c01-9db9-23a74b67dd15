[1;35m10:34:39.701[0;39m [32m[http-nio-10007-exec-1][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLog<PERSON><PERSON>r,148][0;39m - {conn-710001, stmt-720006} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:34:39.768[0;39m [32m[http-nio-10007-exec-1][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:34:39.789[0;39m [32m[http-nio-10007-exec-1][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:00.473[0;39m [32m[nacos-grpc-client-executor-10.203.3.31-105][0;39m [1;31mERROR[0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[printIfErrorEnabled,102][0;39m - [1756089225293_10.203.1.3_53546]Request stream onCompleted, switch server
[1;35m10:39:00.473[0;39m [32m[nacos-grpc-client-executor-10.203.3.31-121][0;39m [1;31mERROR[0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[printIfErrorEnabled,102][0;39m - [1756089202353_10.203.1.3_53370]Request stream onCompleted, switch server
[1;35m10:39:02.650[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710002, stmt-720013} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:02.651[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:02.653[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:25.550[0;39m [32m[http-nio-10007-exec-4][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710003, stmt-720019} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:25.552[0;39m [32m[http-nio-10007-exec-4][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:25.553[0;39m [32m[http-nio-10007-exec-4][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:28.916[0;39m [32m[http-nio-10007-exec-3][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710004, stmt-720025} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:29.633[0;39m [32m[http-nio-10007-exec-3][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:29.636[0;39m [32m[http-nio-10007-exec-3][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:36.060[0;39m [32m[http-nio-10007-exec-5][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710005, stmt-720031} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:36.061[0;39m [32m[http-nio-10007-exec-5][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:36.062[0;39m [32m[http-nio-10007-exec-5][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:38.287[0;39m [32m[http-nio-10007-exec-6][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710006, stmt-720037} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:38.289[0;39m [32m[http-nio-10007-exec-6][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:38.289[0;39m [32m[http-nio-10007-exec-6][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:41.790[0;39m [32m[http-nio-10007-exec-7][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710007, stmt-720043} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:41.792[0;39m [32m[http-nio-10007-exec-7][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:41.794[0;39m [32m[http-nio-10007-exec-7][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:55.208[0;39m [32m[http-nio-10007-exec-1][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710008, stmt-720052} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:55.209[0;39m [32m[http-nio-10007-exec-1][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:55.210[0;39m [32m[http-nio-10007-exec-1][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 103 common frames omitted
[1;35m10:39:57.514[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710009, stmt-720058} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:39:57.516[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:57.518[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:206)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:213)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:199)
	... 101 common frames omitted
[1;35m10:40:21.089[0;39m [32m[http-nio-10007-exec-4][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710010, stmt-720064} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:200)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:214)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:40:21.090[0;39m [32m[http-nio-10007-exec-4][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:40:21.093[0;39m [32m[http-nio-10007-exec-4][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:207)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:214)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:200)
	... 101 common frames omitted
[1;35m10:41:22.869[0;39m [32m[nacos-grpc-client-executor-10.203.3.31-173][0;39m [1;31mERROR[0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[printIfErrorEnabled,102][0;39m - [1756089542445_10.203.1.3_54948]Request stream onCompleted, switch server
[1;35m10:41:22.870[0;39m [32m[nacos-grpc-client-executor-10.203.3.31-184][0;39m [1;31mERROR[0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[printIfErrorEnabled,102][0;39m - [1756089542441_10.203.1.3_54946]Request stream onCompleted, switch server
[1;35m10:41:23.245[0;39m [32m[http-nio-10007-exec-7][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710011, stmt-720076} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:200)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:214)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at jdk.internal.reflect.GeneratedMethodAccessor277.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:41:23.289[0;39m [32m[http-nio-10007-exec-7][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:41:25.036[0;39m [32m[http-nio-10007-exec-7][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:207)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:214)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at jdk.internal.reflect.GeneratedMethodAccessor277.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:200)
	... 100 common frames omitted
[1;35m10:42:15.039[0;39m [32m[nacos-grpc-client-executor-10.203.3.31-213][0;39m [1;31mERROR[0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[printIfErrorEnabled,102][0;39m - [1756089684874_10.203.1.3_55595]Request stream onCompleted, switch server
[1;35m10:42:15.111[0;39m [32m[http-nio-10007-exec-8][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-710012, stmt-720082} execute error. drop table if exists "xqjii_scenex"."tmp_twin_zhu"
;
CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
;
COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
;
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:200)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:214)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at jdk.internal.reflect.GeneratedMethodAccessor277.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m10:42:15.112[0;39m [32m[http-nio-10007-exec-8][0;39m [1;31mERROR[0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[executeSql,820][0;39m - Clause 21 SQL execution failed: 
CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:42:15.113[0;39m [32m[http-nio-10007-exec-8][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
java.lang.RuntimeException: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:207)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.invoke(AlterJrmDdl.java:214)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl.modifyForm(TwinClassServiceImpl.java:273)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.uino.x.pedestal.twin.impl.TwinClassServiceImpl$$SpringCGLIB$$0.modifyForm(<generated>)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController.modifyForm(TwinClassController.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at jdk.internal.reflect.GeneratedMethodAccessor277.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.TwinClassController$$SpringCGLIB$$0.modifyForm(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor257.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.BatchUpdateException: 第1 行附近出现错误:
索引[LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37]已存在
	at dm.jdbc.driver.DBError.throwBatchUpdateException(SourceFile:761)
	at dm.jdbc.driver.DmdbStatement.do_executeBatch(SourceFile:323)
	at dm.jdbc.driver.DmdbStatement.executeBatch(SourceFile:1517)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3101)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeBatch(FilterAdapter.java:2506)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeBatch(FilterEventAdapter.java:273)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeBatch(FilterChainImpl.java:3099)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeBatch(StatementProxyImpl.java:196)
	at com.alibaba.druid.pool.DruidPooledStatement.executeBatch(DruidPooledStatement.java:794)
	at com.uino.x.common.datasource.util.SqlUtils.executeSql(SqlUtils.java:808)
	at com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl.generateTempTable(AlterJrmDdl.java:200)
	... 100 common frames omitted
[1;35m11:11:51.758[0;39m [32m[main][0;39m [1;31mERROR[0;39m [36mo.s.b.SpringApplication[0;39m - [36m[reportFailure,857][0;39m - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'initConfigRunner' defined in file [D:\x\pedestal-x\pedestal-x-app\scene-x\target\classes\com\uino\x\pedestal\app\scenex\config\InitConfigRunner.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'tenantInfoMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\tenant\tenant-dao\target\classes\com\uino\x\pedestal\tenant\dao\mapper\TenantInfoMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at com.uino.x.pedestal.app.scenex.SceneXApplication.main(SceneXApplication.java:34)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tenantInfoMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\tenant\tenant-dao\target\classes\com\uino\x\pedestal\tenant\dao\mapper\TenantInfoMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1450)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1683)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1826)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:315)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1541)
	... 30 common frames omitted
Caused by: org.flywaydb.core.api.exception.FlywayValidateException: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:201)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:210)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:188)
	at com.uino.x.common.redis.redisson.FlywayRedissonMigrationStrategy.migrate(FlywayRedissonMigrationStrategy.java:37)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:62)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	... 42 common frames omitted
