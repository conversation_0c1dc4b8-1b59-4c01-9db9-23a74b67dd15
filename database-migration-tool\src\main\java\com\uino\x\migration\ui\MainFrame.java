package com.uino.x.migration.ui;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.awt.image.BufferedImage;

import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;

import com.uino.x.migration.config.ConfigManager;
import com.uino.x.migration.service.DatabaseService;

import lombok.extern.slf4j.Slf4j;

/**
 * 主界面框架
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Slf4j
public class MainFrame extends JFrame {
    
    private final DatabaseService databaseService;
    
    private JTabbedPane tabbedPane;
    private CombinedDatabaseConfigPanel databaseConfigPanel;
    private MigrationPanel migrationPanel;
    private OneClickMigrationPanel oneClickMigrationPanel;
    private FlywayValidationPanel flywayValidationPanel;
    private TimerPathFixPanel timerPathFixPanel;
    private SysConfigModifyPanel sysConfigModifyPanel;
    private SqlTransformationPanel sqlTransformationPanel;
    private SqlExecutionPanel sqlExecutionPanel;
    private ConfigFilePanel configFilePanel;
    private LogPanel logPanel;
    private AboutPanel aboutPanel;
    private ConfigManager.AppConfig appConfig;

    public MainFrame(DatabaseService databaseService) {
        this.databaseService = databaseService;
        loadConfiguration();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setVisible(true);
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setTitle("数据库迁移工具 v1.0.0");
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        setSize(1200, 900); // 增大窗口尺寸，提供更好的使用体验
        setLocationRelativeTo(null);
        
        // 设置应用图标
        try {
            setIconImage(createDefaultIcon());
        } catch (Exception e) {
            log.warn("无法设置应用图标", e);
        }
        
        // 创建选项卡面板
        tabbedPane = new JTabbedPane();
        
        // 创建各个面板
        databaseConfigPanel = new CombinedDatabaseConfigPanel(databaseService);
        migrationPanel = new MigrationPanel(databaseService, databaseConfigPanel);
        oneClickMigrationPanel = new OneClickMigrationPanel(databaseService, databaseConfigPanel);
        flywayValidationPanel = new FlywayValidationPanel(databaseService, databaseConfigPanel);
        timerPathFixPanel = new TimerPathFixPanel(databaseService, databaseConfigPanel);
        sysConfigModifyPanel = new SysConfigModifyPanel(databaseService, databaseConfigPanel);
        sqlTransformationPanel = new SqlTransformationPanel();
        sqlExecutionPanel = new SqlExecutionPanel(databaseService, databaseConfigPanel);
        configFilePanel = new ConfigFilePanel();
        logPanel = new LogPanel();
        aboutPanel = new AboutPanel();

        // 先初始化数据库配置列表
        migrationPanel.initializeDatabaseConfigs();
        oneClickMigrationPanel.initializeDatabaseConfigs();
        flywayValidationPanel.initializeDatabaseConfigs();
        timerPathFixPanel.initializeDatabaseConfigs();
        sysConfigModifyPanel.initializeDatabaseConfigs();
        sqlExecutionPanel.initializeDatabaseConfigs();

        // 然后应用配置，这样可以正确回显选择的数据库配置
        applyConfigurationToComponents();
        
        // 添加选项卡
        tabbedPane.addTab("数据库配置", databaseConfigPanel);
        tabbedPane.addTab("数据迁移", migrationPanel);
        tabbedPane.addTab("一键迁移到精简", oneClickMigrationPanel);
        tabbedPane.addTab("flyway校验修复", flywayValidationPanel);
        tabbedPane.addTab("定时任务路径修复", timerPathFixPanel);
        tabbedPane.addTab("系统配置修改", sysConfigModifyPanel);
        tabbedPane.addTab("SQL转换", sqlTransformationPanel);
        tabbedPane.addTab("SQL执行", sqlExecutionPanel);
        tabbedPane.addTab("配置文件", configFilePanel);
        tabbedPane.addTab("操作日志", logPanel);
        tabbedPane.addTab("关于", aboutPanel);
        
        // 设置选项卡位置
        tabbedPane.setTabPlacement(JTabbedPane.TOP);

        // 设置数据库配置面板的保存回调
        databaseConfigPanel.setSaveConfigCallback(() -> saveConfiguration());

        // 设置数据迁移面板的配置回调
        migrationPanel.setConfigCallback(() -> saveConfiguration());

        // 设置一键迁移面板的配置回调
        oneClickMigrationPanel.setConfigCallback(() -> saveConfiguration());

        // 设置flyway校验修复面板的配置回调
        flywayValidationPanel.setConfigCallback(() -> saveConfiguration());

        // 设置定时任务路径修复面板的配置回调
        timerPathFixPanel.setConfigCallback(() -> saveConfiguration());

        // 设置系统配置修改面板的配置回调
        sysConfigModifyPanel.setConfigCallback(() -> saveConfiguration());

        // 设置SQL执行面板的配置回调
        sqlExecutionPanel.setConfigCallback(() -> saveConfiguration());

        // 添加窗口关闭监听器，保存配置
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                saveConfiguration();
                System.exit(0);
            }
        });
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());

        // 添加主面板
        add(tabbedPane, BorderLayout.CENTER);

        // 创建状态栏
        JPanel statusBar = createStatusBar();
        add(statusBar, BorderLayout.SOUTH);
    }
    
    /**
     * 创建关于面板
     */
    private JPanel createAboutPanel() {
        JPanel aboutPanel = new JPanel(new BorderLayout());
        aboutPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // 创建主要内容面板
        JPanel contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));
        contentPanel.setAlignmentX(Component.CENTER_ALIGNMENT);

        // 应用标题
        JLabel titleLabel = new JLabel("数据库迁移工具");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 24));
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(titleLabel);

        contentPanel.add(Box.createVerticalStrut(10));

        // 版本信息
        JLabel versionLabel = new JLabel("版本 v1.0.0");
        versionLabel.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        versionLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(versionLabel);

        contentPanel.add(Box.createVerticalStrut(20));

        // 功能描述
        JTextArea descriptionArea = new JTextArea();
        descriptionArea.setText("这是一个功能强大的数据库迁移工具，支持：\n\n" +
                "• 多种数据库类型连接\n" +
                "• 数据导出和导入\n" +
                "• 一键迁移到精简功能\n" +
                "• Schema映射配置\n" +
                "• 操作日志记录\n\n" +
                "适用于数据库数据迁移、备份和同步等场景。");
        descriptionArea.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        descriptionArea.setEditable(false);
        descriptionArea.setOpaque(false);
        descriptionArea.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(descriptionArea);

        contentPanel.add(Box.createVerticalStrut(20));

        // 版权信息
        JLabel copyrightLabel = new JLabel("© 2025 数据库迁移工具");
        copyrightLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        copyrightLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        contentPanel.add(copyrightLabel);

        aboutPanel.add(contentPanel, BorderLayout.CENTER);

        return aboutPanel;
    }
    
    /**
     * 创建状态栏
     */
    private JPanel createStatusBar() {
        JPanel statusBar = new JPanel(new BorderLayout());
        statusBar.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JLabel statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));
        statusBar.add(statusLabel, BorderLayout.WEST);
        
        // 保存状态标签引用，以便更新
        migrationPanel.setStatusLabel(statusLabel);
        
        return statusBar;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                exitApplication();
            }
        });
        
        // 选项卡切换事件
        tabbedPane.addChangeListener(e -> {
            int selectedIndex = tabbedPane.getSelectedIndex();
            if (selectedIndex == 8) { // 配置文件选项卡
                configFilePanel.refresh();
            } else if (selectedIndex == 9) { // 操作日志选项卡
                logPanel.refreshLogs();
            }
        });
    }
    

    
    /**
     * 退出应用程序
     */
    private void exitApplication() {
        int option = JOptionPane.showConfirmDialog(this,
            "确定要退出数据库迁移工具吗？",
            "确认退出",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE);
        
        if (option == JOptionPane.YES_OPTION) {
            log.info("用户退出应用程序");
            System.exit(0);
        }
    }
    
    /**
     * 创建默认图标
     */
    private Image createDefaultIcon() {
        return createDefaultIcon(32);
    }
    
    private Image createDefaultIcon(int size) {
        // 创建一个简单的默认图标
        Image image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = (Graphics2D) image.getGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制数据库图标
        g2d.setColor(new Color(70, 130, 180));
        g2d.fillRoundRect(2, 2, size-4, size-4, 8, 8);
        
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("Arial", Font.BOLD, size/3));
        FontMetrics fm = g2d.getFontMetrics();
        String text = "DB";
        int x = (size - fm.stringWidth(text)) / 2;
        int y = (size + fm.getAscent()) / 2;
        g2d.drawString(text, x, y);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 创建选项卡图标
     */
    private Icon createTabIcon(String type) {
        return new ImageIcon(createDefaultIcon(16));
    }

    /**
     * 加载配置
     */
    private void loadConfiguration() {
        appConfig = ConfigManager.loadConfig();
        log.info("配置已加载");
    }



    /**
     * 应用配置到组件（在组件创建后立即调用）
     */
    private void applyConfigurationToComponents() {
        if (appConfig == null) {
            log.info("应用配置到组件：appConfig为null，跳过配置应用");
            return;
        }

        log.info("开始应用配置到组件");

        // 应用迁移配置
        if (appConfig.getMigrationConfig() != null) {
            migrationPanel.setMigrationConfig(appConfig.getMigrationConfig());
        }

        // 应用一键迁移配置
        if (appConfig.getOneClickMigrationConfig() != null) {
            oneClickMigrationPanel.setOneClickMigrationConfig(appConfig.getOneClickMigrationConfig());
        }

        // 应用flyway校验修复配置
        if (appConfig.getFlywayValidationConfig() != null) {
            flywayValidationPanel.setFlywayValidationConfig(appConfig.getFlywayValidationConfig());
        }

        // 应用定时任务路径修复配置
        if (appConfig.getTimerPathFixConfig() != null) {
            timerPathFixPanel.setTimerPathFixConfig(appConfig.getTimerPathFixConfig());
        }

        // 应用系统配置修改配置
        if (appConfig.getSysConfigModifyConfig() != null) {
            sysConfigModifyPanel.setSysConfigModifyConfig(appConfig.getSysConfigModifyConfig());
        }

        // 应用SQL执行配置
        if (appConfig.getSqlExecutionConfig() != null) {
            sqlExecutionPanel.setSqlExecutionConfig(appConfig.getSqlExecutionConfig());
        }

        log.info("配置已应用到组件");
    }

    /**
     * 应用配置到UI组件（保持向后兼容）
     */
    private void applyConfiguration() {
        applyConfigurationToComponents();
    }

    /**
     * 保存配置
     */
    private void saveConfiguration() {
        // 始终从文件重新加载配置，以保留命名配置
        appConfig = ConfigManager.loadConfig();

        // 先获取并保存应用程序级别的配置（在刷新配置列表之前）
        ConfigManager.MigrationConfig migrationConfig = migrationPanel.getMigrationConfig();
        ConfigManager.OneClickMigrationConfig oneClickConfig = oneClickMigrationPanel.getOneClickMigrationConfig();
        ConfigManager.FlywayValidationConfig flywayConfig = flywayValidationPanel.getFlywayValidationConfig();
        ConfigManager.TimerPathFixConfig timerConfig = timerPathFixPanel.getTimerPathFixConfig();
        ConfigManager.SysConfigModifyConfig sysConfig = sysConfigModifyPanel.getSysConfigModifyConfig();
        ConfigManager.SqlExecutionConfig sqlExecutionConfig = sqlExecutionPanel.getSqlExecutionConfig();

        // 设置配置到appConfig
        appConfig.setMigrationConfig(migrationConfig);
        appConfig.setOneClickMigrationConfig(oneClickConfig);
        appConfig.setFlywayValidationConfig(flywayConfig);
        appConfig.setTimerPathFixConfig(timerConfig);
        appConfig.setSysConfigModifyConfig(sysConfig);
        appConfig.setSqlExecutionConfig(sqlExecutionConfig);

        // 保存到文件
        ConfigManager.saveConfig(appConfig);

        // 刷新配置文件面板
        configFilePanel.refresh();

        // 刷新所有面板的数据库配置列表
        migrationPanel.initializeDatabaseConfigs();
        oneClickMigrationPanel.initializeDatabaseConfigs();
        flywayValidationPanel.initializeDatabaseConfigs();
        timerPathFixPanel.initializeDatabaseConfigs();
        sysConfigModifyPanel.initializeDatabaseConfigs();
        sqlExecutionPanel.initializeDatabaseConfigs();

        // 重新应用配置到UI组件，确保selectedDatabaseConfig正确回显
        applyConfigurationToComponents();

        log.info("配置已保存");
    }
}
