[1;35m10:31:40.000[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m10:31:40.080[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m10:31:40.200[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m10:31:40.311[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m10:31:40.322[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStarting,53][0;39m - Starting SystemXApplication using Java 17.0.14 with PID 23360 (D:\x\pedestal-x\pedestal-x-app\system-x\target\classes started by Administrator in D:\x)
[1;35m10:31:40.325[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal"
[1;35m10:31:40.403[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m10:31:40.404[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m10:31:40.404[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=system-x.yml, group=DEFAULT_GROUP] success
[1;35m10:31:43.089[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'microAppFallbackFactory' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=microAppApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/microapp/api/config/MicroAppApiConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=projectPackApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/projectpack/api/config/ProjectPackApiConfig.class]]
[1;35m10:31:43.222[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.223[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.224[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.225[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.226[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.227[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.228[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.230[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.231[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.233[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.236[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.237[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.239[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.241[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.242[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.244[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.245[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.247[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.250[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.251[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.255[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.258[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.260[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.261[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.262[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.262[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.264[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.264[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.265[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.265[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.266[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.266[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.267[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.268[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.268[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.269[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.269[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.270[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.270[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.271[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.272[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.275[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.277[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.279[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.annotate:annotate}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.282[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.file:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.283[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.283[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.284[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.285[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.286[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.log:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.288[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.tenant:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.292[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.bubble:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.294[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.config:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.305[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.timer:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.310[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.effectpackage:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.314[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.viewpoint:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:31:43.651[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'paginationInnerInterceptor' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=mybatisConfig; factoryMethodName=paginationInnerInterceptor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/app/systemx/config/MybatisConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.uino.x.common.dameng.CommonXDmConfig; factoryMethodName=paginationInnerInterceptor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/common/dameng/CommonXDmConfig.class]]
[1;35m10:31:44.240[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m10:31:44.252[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[1;35m10:31:44.320[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 46 ms. Found 0 Elasticsearch repository interfaces.
[1;35m10:31:44.333[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m10:31:44.335[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[1;35m10:31:44.357[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
[1;35m10:31:44.374[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m10:31:44.376[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m10:31:44.409[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
[1;35m10:31:45.141[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=7c093ee1-8259-3a0a-bbd8-0ac350c6c415
[1;35m10:31:45.214[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.s.SqlTransformationStrategyFactory[0;39m - [36m[createReplaceIntoStrategy,215][0;39m - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
[1;35m10:31:45.219[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.s.SqlTransformationStrategyFactory[0;39m - [36m[createDefaultStrategies,103][0;39m - Created 9 default transformation strategies
[1;35m10:31:45.227[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[logStrategyRegistration,625][0;39m - Registered 9 transformation strategies in engine [dm]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, BacktickQuoteTransformationStrategy]
[1;35m10:31:45.228[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[<init>,235][0;39m - SQL transformation engine [dm] initialized with 9 strategies
[1;35m10:31:45.276[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-1} inited
[1;35m10:31:45.413[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,346][0;39m - SQL transformation statistics: 
SqlTransformationEngine [dm] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 9 registered
  Memory: Estimated Size=0 bytes
[1;35m10:31:45.492[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:31:45.526[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-1} closing ...
[1;35m10:31:45.539[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-1} closed
[1;35m10:31:48.737[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 10004 (http)
[1;35m10:31:48.798[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-10004"]
[1;35m10:31:48.803[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m10:31:48.803[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.43]
[1;35m10:31:48.994[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m10:31:48.994[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 8587 ms
[1;35m10:31:51.820[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m10:31:53.311[0;39m [32m[redisson-netty-1-4][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m10:31:53.335[0;39m [32m[redisson-netty-1-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m10:31:53.936[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m10:31:53.981[0;39m [32m[redisson-netty-5-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m10:31:54.019[0;39m [32m[redisson-netty-5-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m10:31:54.775[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [systemx] success
[1;35m10:31:54.776[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[afterPropertiesSet,241][0;39m - dynamic-datasource initial loaded [1] datasource,primary datasource named [systemx]
[1;35m10:31:54.925[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-2,systemx} inited
[1;35m10:31:54.928[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:31:54","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:31:57.611[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:31:57.793[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.159s)
[1;35m10:31:57.829[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "systemx": 2023.11.15.09.00.00
[1;35m10:31:57.834[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "systemx" is up to date. No migration necessary.
[1;35m10:31:58.602[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:31:58.820[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - micro-app-service-impl executor is create!
[1;35m10:31:59.510[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - tenant-info-service-impl executor is create!
[1;35m10:32:00.133[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,781][0;39m - Authorization initialized
[1;35m10:32:00.133[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,782][0;39m - License status : Disabled
[1;35m10:32:23.384[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Directory info : D:\x
[1;35m10:32:23.385[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk usage : 10179795357
[1;35m10:32:23.386[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk info : Drive > D:\    Total space > 70179434496    Free space > 21295841280
[1;35m10:32:23.386[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Network info : {eth5={hostname=DESKTOP-38F0Q6A, ip=**********, network-arch=Realtek Gaming GbE Family Controller, ipnet=ipv4, mac=18-C0-4D-EE-B7-42}}
[1;35m10:32:23.386[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU ID : null
[1;35m10:32:23.386[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU core : **********
[1;35m10:32:23.386[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System : Windows 11
[1;35m10:32:23.387[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System version : 10.0
[1;35m10:32:23.387[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System architecture: amd64
[1;35m10:32:23.387[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System hostname : DESKTOP-38F0Q6A
[1;35m10:32:23.387[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Total memory : 51397271552
[1;35m10:32:23.387[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - User Info : [must]
companyNameZh=优锘
mmdID=uino
validityPeriod=development
[other]
companyNameEn=uino
releaseTime=************
[1;35m10:32:23.492[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.l.i.ProductLicenseInfoServiceImpl[0;39m - [36m[<clinit>,145][0;39m - 当前机器标识: **********,**********
[1;35m10:32:23.906[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:24.550[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - sys-user-service-impl executor is create!
[1;35m10:32:25.027[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:25.931[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:26.025[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:26.050[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - device-alert-service-impl executor is create!
[1;35m10:32:26.297[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - sys-dict-data-service-impl executor is create!
[1;35m10:32:26.595[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'distribution' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.399[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.427[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.440[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.455[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.494[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.508[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.516[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.631[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:27.656[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - sys-bubble-info-service-impl executor is create!
[1;35m10:32:29.004[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'log-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:29.085[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - device-performance-service-impl executor is create!
[1;35m10:32:29.149[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:29.177[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:29.188[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:29.371[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:29.461[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - project-pack-service-impl executor is create!
[1;35m10:32:29.659[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'edtap-server' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:29.671[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - data-migration-service-impl executor is create!
[1;35m10:32:29.719[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'ti-dix' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:32:30.876[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.w.s.f.ReflectionServiceFactoryBean[0;39m - [36m[buildServiceFromClass,436][0;39m - Creating Service {http://provisioning.idm.shenhua.com}ProvisioningWSService from class com.uino.x.pedestal.app.systemx.webservice.ProvisioningWSServicePortType
[1;35m10:32:31.793[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.e.ServerImpl[0;39m - [36m[initDestination,95][0;39m - Setting the server's publish address to be /ProvisioningService
[1;35m10:32:31.961[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m10:32:31.993[0;39m [32m[redisson-netty-8-5][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m10:32:32.016[0;39m [32m[redisson-netty-8-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m10:32:32.286[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.WebMvcConfig[0;39m - [36m[addInterceptors,55][0;39m - 输出白名单: [/favicon.ico, /v3/api-docs/**, /doc.html, /webjars/**, /webSocket, /sys-user/get-user-by-account, /sys-user/update-by-id, /sys-user/save-by-default, /sys-role/get-role-code-list, /sys-menu/get-login-permissions, /sys-user/get-sync-user-list, /sys-user/find-ldap-user-by-account, /sys-user-detail/get-sync-user-detail-list, /sys-dept/get-sync-dept-list, /tenant-info/get-sync-tenant-list, /tenant-info/get-multi-tenant-enable, /tenant-info/sync-app-menu, /sys-identity/ldap, /sys-identity/find-enable-ldap, /sys-identity/add-ldap-user, /sys-identity/check-forbid-ip, /web-assets/xxv-web-assets-list, /sys-config/load-xxv-configs, /sys-file-info/get-bucket-value-name, /tenant-info/feign-add, /tenant-info/feign-edit, /tenant-info/feign-delete, /tenant-info/get-sync-tenant-list1, /tenant-info/feign-edit1, /tenant-info/delete, /sys-user/update-pwd, /sys-user/update-project-pwd, /web-assets/sync, /data-migration/create-database, /data-migration/upload-database-info, /data-migration/feign-download-database-info, /data-migration/download-database-info, /data-migration/feign-download-file, /data-migration/do-export-data, /data-migration/download-file, /tenant-info/get-tenant-by-code, /druid/**, /druid/index.html, /project-pack/detail-pack/**, /sys-config/open, /license-info/validate-license, /license-info/get-license-info, /license-info/upload-license, /license-info/thing-js-api, /license-info/thing-js-2-api, /license-info/get-master-license-info, /auth/**, /sync/**]
[1;35m10:32:32.288[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.SentinelWebMvcConfigurer[0;39m - [36m[addInterceptors,52][0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[1;35m10:32:33.517[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.m.e.s.MybatisPlusApplicationContextAware[0;39m - [36m[setApplicationContext,40][0;39m - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@33feda48
[1;35m10:32:35.478[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 1 endpoint beneath base path '/actuator'
[1;35m10:32:36.060[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Starting ProtocolHandler ["http-nio-10004"]
[1;35m10:32:36.085[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[start,243][0;39m - Tomcat started on port 10004 (http) with context path '/'
[1;35m10:32:36.090[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[init,102][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m10:32:36.091[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[initNamespaceForNaming,62][0;39m - initializer namespace from ans.namespace attribute : null
[1;35m10:32:36.092[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$0,66][0;39m - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[1;35m10:32:36.093[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$1,73][0;39m - initializer namespace from namespace attribute :null
[1;35m10:32:36.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,75][0;39m - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[1;35m10:32:36.104[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m10:32:36.104[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m10:32:36.316[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 51dea487-3136-46fb-978b-5a370d792ee9
[1;35m10:32:36.318[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,109][0;39m - Create naming rpc client for uuid->51dea487-3136-46fb-978b-5a370d792ee9
[1;35m10:32:36.318[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[1;35m10:32:36.319[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[1;35m10:32:36.319[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[1;35m10:32:36.320[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m10:32:36.320[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:32:36.359[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Success to connect to server [***********:8848] on start up, connectionId = 1756089156169_**********_53149
[1;35m10:32:36.360[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Notify connected event to listeners.
[1;35m10:32:36.360[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m10:32:36.360[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m10:32:36.360[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$377/0x000001e0c8421b80
[1;35m10:32:36.363[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[registerService,133][0;39m - [REGISTER-SERVICE] pedestal registering service system-x with instance Instance{instanceId='null', ip='**********', port=10004, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[1;35m10:32:36.391[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[register,76][0;39m - nacos registry, DEFAULT_GROUP system-x **********:10004 register finished
[1;35m10:32:36.669[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStarted,59][0;39m - Started SystemXApplication in 60.695 seconds (process running for 62.731)
[1;35m10:32:36.687[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - async-utils executor is create!
[1;35m10:32:36.694[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_server_info' on lock
[1;35m10:32:36.694[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:32:36.694[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'master:systemx_init' on lock
[1;35m10:32:37.044[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.l.i.ProductLicenseInfoServiceImpl[0;39m - [36m[printDeploymentLog,578][0;39m - LICENSE SERVER INFO  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
[1;35m10:32:37.045[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Directory info : D:\x
[1;35m10:32:37.045[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk usage : 10179795357
[1;35m10:32:37.045[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk info : Drive > D:\    Total space > 70179434496    Free space > 21295841280
[1;35m10:32:37.046[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Network info : {eth5={hostname=DESKTOP-38F0Q6A, ip=**********, network-arch=Realtek Gaming GbE Family Controller, ipnet=ipv4, mac=18-C0-4D-EE-B7-42}}
[1;35m10:32:37.046[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU ID : null
[1;35m10:32:37.046[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU core : **********
[1;35m10:32:37.046[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System : Windows 11
[1;35m10:32:37.046[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System version : 10.0
[1;35m10:32:37.047[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System architecture: amd64
[1;35m10:32:37.047[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System hostname : DESKTOP-38F0Q6A
[1;35m10:32:37.047[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Total memory : 51397271552
[1;35m10:32:37.047[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - User Info : [must]
companyNameZh=优锘
mmdID=uino
validityPeriod=development
[other]
companyNameEn=uino
releaseTime=************
[1;35m10:32:37.070[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_server_info' is unlocked
[1;35m10:32:37.111[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.tables where table_name = 'tenant_info' and table_schema = 'systemx';]
Result:   [SELECT count(*) FROM all_tables WHERE table_name = 'tenant_info' AND owner = 'systemx';]
[1;35m10:32:37.197[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m10:32:37.197[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m10:32:37.254[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102307分钟
[1;35m10:32:37.258[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:32:37.313[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - tenant-info-service-impl executor is create!
[1;35m10:32:37.318[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:32:37.318[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:32:37.345[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(2) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:32:37.360[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:32:37.481[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-3,bzmmx_systemx_1959806087399264258} inited
[1;35m10:32:37.481[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=bzmmx_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"bzmmx_systemx_1959806087399264258","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:32:37","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:32:37.482[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:32:37.483[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-4,project_systemx_1959806087445401602} inited
[1;35m10:32:37.483[0;39m [32m[Druid-ConnectionPool-Log-1640524098][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=project_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"project_systemx_1959806087445401602","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:32:37","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:32:37.484[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:32:37.484[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-5,xqjii_systemx_1959806087441207298} inited
[1;35m10:32:37.485[0;39m [32m[Druid-ConnectionPool-Log-889673280][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806087441207298","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:32:37","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:32:37.485[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:32:37.681[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[1;35m10:32:37.681[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,532][0;39m - Initializing Servlet 'dispatcherServlet'
[1;35m10:32:37.684[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.168s)
[1;35m10:32:37.684[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.168s)
[1;35m10:32:37.684[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,554][0;39m - Completed initialization in 3 ms
[1;35m10:32:37.709[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.188s)
[1;35m10:32:37.747[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "xqjii_systemx": 2023.11.15.09.00.00
[1;35m10:32:37.757[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "xqjii_systemx" is up to date. No migration necessary.
[1;35m10:32:37.762[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "bzmmx_systemx": 2023.11.15.09.00.00
[1;35m10:32:37.763[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "project_systemx": 2023.11.15.09.00.00
[1;35m10:32:37.770[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "bzmmx_systemx" is up to date. No migration necessary.
[1;35m10:32:37.771[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "project_systemx" is up to date. No migration necessary.
[1;35m10:32:37.772[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-5} closing ...
[1;35m10:32:37.773[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-5} closed
[1;35m10:32:37.773[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:32:37.788[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-4} closing ...
[1;35m10:32:37.788[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-3} closing ...
[1;35m10:32:37.788[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-3} closed
[1;35m10:32:37.788[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-4} closed
[1;35m10:32:37.793[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [xqjii_systemx] success
[1;35m10:32:37.812[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [project_systemx] success
[1;35m10:32:37.825[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [bzmmx_systemx] success
[1;35m10:32:37.832[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT
            d.`value`
        FROM
            sys_dict_data d
                LEFT JOIN sys_dict_type t ON d.type_id = t.id
        WHERE
            t.`code` = 'BUCKET'
            AND d.`status` != 2
	        AND d.`code` = ?]
Result:   [SELECT d."value" FROM sys_dict_data d LEFT JOIN sys_dict_type t ON d.type_id = t.id WHERE t."code" = 'BUCKET' AND d."status" != 2 AND d."code" = ?]
[1;35m10:32:37.850[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$13,638][0;39m - xqjii 租户数据初始化成功
[1;35m10:32:37.850[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$13,638][0;39m - bzmmx 租户数据初始化成功
[1;35m10:32:37.850[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$13,638][0;39m - project 租户数据初始化成功
[1;35m10:32:37.851[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:32:37.903[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT
            d.`value`
        FROM
            sys_dict_data d
                LEFT JOIN sys_dict_type t ON d.type_id = t.id
        WHERE
            t.`code` = 'BUCKET'
            AND d.`status` != 2
	        AND d.`code` = ?]
Result:   [SELECT d."value" FROM sys_dict_data d LEFT JOIN sys_dict_type t ON d.type_id = t.id WHERE t."code" = 'BUCKET' AND d."status" != 2 AND d."code" = ?]
[1;35m10:32:37.915[0;39m [32m[nacos-grpc-client-executor-***********-13][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Receive server push request, request = NotifySubscriberRequest, requestId = 7204
[1;35m10:32:37.916[0;39m [32m[nacos-grpc-client-executor-***********-13][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Ack server push request, request = NotifySubscriberRequest, requestId = 7204
[1;35m10:32:37.957[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-6,aaaaa_systemx_1959806089605468161} inited
[1;35m10:32:37.958[0;39m [32m[Druid-ConnectionPool-Log-1998320755][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=aaaaa_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"aaaaa_systemx_1959806089605468161","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:32:37","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:32:37.959[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:32:38.132[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.133s)
[1;35m10:32:38.172[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "aaaaa_systemx": 2023.11.15.09.00.00
[1;35m10:32:38.178[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "aaaaa_systemx" is up to date. No migration necessary.
[1;35m10:32:38.184[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-6} closing ...
[1;35m10:32:38.185[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-6} closed
[1;35m10:32:38.205[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [aaaaa_systemx] success
[1;35m10:32:38.217[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$13,638][0;39m - aaaaa 租户数据初始化成功
[1;35m10:32:38.222[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[shutdown,150][0;39m - tenant-info-service-impl executor is shutdown!
[1;35m10:32:38.225[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'master:systemx_init' is unlocked
[1;35m10:32:38.678[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[initNotifyWarnTimeout,72][0;39m - config listener notify warn timeout millis use default 60000 millis 
[1;35m10:32:38.678[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[<clinit>,99][0;39m - nacos.cache.data.init.snapshot = true 
[1;35m10:32:38.691[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-config.yml+DEFAULT_GROUP+pedestal
[1;35m10:32:38.692[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-config.yml, group=DEFAULT_GROUP, cnt=1
[1;35m10:32:38.693[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-config.yml, group=DEFAULT_GROUP
[1;35m10:32:38.703[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-info.yml+DEFAULT_GROUP+pedestal
[1;35m10:32:38.703[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-info.yml, group=DEFAULT_GROUP, cnt=1
[1;35m10:32:38.703[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-info.yml, group=DEFAULT_GROUP
[1;35m10:32:38.715[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] system-x.yml+DEFAULT_GROUP+pedestal
[1;35m10:32:38.715[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=system-x.yml, group=DEFAULT_GROUP, cnt=1
[1;35m10:32:38.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=system-x.yml, group=DEFAULT_GROUP
[1;35m10:32:38.875[0;39m [32m[RMI TCP Connection(3)-198.18.0.1][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m10:32:54.938[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"activePeak":4,"activePeakTime":"2025-08-25 10:32:37","poolingCount":4,"poolingPeak":4,"poolingPeakTime":"2025-08-25 10:32:37","connectCount":24,"closeCount":23,"notEmptyWaitCount":4,"notEmptyWaitMillis":47,"physicalConnectCount":4,"executeCount":30,"commitCount":5,"pstmtCacheHitCount":11,"pstmtCacheMissCount":20,"startTransactionCount":5,"transactionHistogram":[0,4,1],"connectionHoldTimeHistogram":[0,14,7,2],"clobOpenCount":47,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":4,"executeMillisMax":2,"executeMillisTotal":6,"executeHistogram":[1,3],"executeAndResultHoldHistogram":[1,3],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') FROM DUAL","executeCount":4,"executeMillisMax":0,"executeMillisTotal":2,"executeHistogram":[4],"executeAndResultHoldHistogram":[4],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT USER FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT CASE WHEN EXISTS(SELECT DISTINCT OBJECT_NAME FROM ALL_OBJECTS WHERE OBJECT_TYPE = 'SCH' AND OBJECT_NAME = ?) THEN 1 ELSE 0 END FROM DUAL","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2],"inTransactionCount":1},{"sql":"SELECT \"installed_rank\",\"version\",\"description\",\"type\",\"script\",\"checksum\",\"installed_on\",\"installed_by\",\"execution_time\",\"success\" FROM \"systemx\".\"flyway_schema_history\" WHERE \"installed_rank\" > ? ORDER BY \"installed_rank\"","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[1,1],"concurrentMax":1,"fetchRowCount":25,"fetchRowCountMax":25,"fetchRowHistogram":[1,0,1],"inTransactionCount":2},{"sql":"LOCK TABLE \"systemx\".\"flyway_schema_history\" IN EXCLUSIVE MODE","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"inTransactionCount":1},{"sql":"ALTER SESSION SET CURRENT_SCHEMA=\"systemx\"","executeCount":2,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[2],"executeAndResultHoldHistogram":[2],"concurrentMax":1,"inTransactionCount":2},{"sql":"SELECT count(*) FROM all_tables WHERE table_name = 'tenant_info' AND owner = 'systemx';","executeCount":1,"executeMillisMax":7,"executeMillisTotal":7,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,name,code,license_update_time,license_expired_time,license_total_time,scene_count,user_count,remark,status,create_time,create_user,update_time,update_user  FROM tenant_info      WHERE  (status <> ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":4,"fetchRowHistogram":[0,1],"clobOpenCount":4},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":8,"executeMillisMax":16,"executeMillisTotal":52,"executeHistogram":[0,6,2],"executeAndResultHoldHistogram":[0,6,2],"concurrentMax":2,"fetchRowCount":8,"fetchRowCountMax":1,"fetchRowHistogram":[0,8]},{"sql":"SELECT  id,timer_name,action_class,cron,job_status,task_form,task_args,task_type,remark,status,create_time,create_user,update_time,update_user  FROM sys_timers      WHERE  (status <> ? AND job_status = ?)","executeCount":5,"executeMillisMax":1,"executeMillisTotal":7,"executeHistogram":[0,5],"executeAndResultHoldHistogram":[0,5],"concurrentMax":2,"fetchRowCount":20,"fetchRowCountMax":4,"fetchRowHistogram":[0,5],"clobOpenCount":40},{"sql":"SELECT d.\"value\" FROM sys_dict_data d LEFT JOIN sys_dict_type t ON d.type_id = t.id WHERE t.\"code\" = 'BUCKET' AND d.\"status\" != 2 AND d.\"code\" = ?","executeCount":3,"executeMillisMax":15,"executeMillisTotal":30,"executeHistogram":[0,1,2],"executeAndResultHoldHistogram":[0,1,2],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3],"clobOpenCount":3},{"sql":"SELECT  id,twin_class_id,twin_class_code,acquisition_frequency,acquisition_frequency_cron,description,enable,status,create_time,create_user,update_time,update_user  FROM indicator      WHERE  (enable = ? AND status = ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1}]}
[1;35m10:32:56.449[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-7,xqjii_systemx_1959806089290895361} inited
[1;35m10:32:56.449[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:32:56","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:32:56.449[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:33:36.668[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:33:36.707[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102309分钟
[1;35m10:33:36.732[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:33:54.939[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":4,"connectCount":0,"closeCount":0}
[1;35m10:33:56.450[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:32:56","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:32:56","connectCount":24,"closeCount":24,"executeCount":24,"pstmtCacheHitCount":21,"pstmtCacheMissCount":3,"connectionHoldTimeHistogram":[0,24],"clobOpenCount":74,"sqlList":[{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":12,"executeMillisMax":9,"executeMillisTotal":44,"executeHistogram":[0,12],"executeAndResultHoldHistogram":[0,12],"concurrentMax":1,"fetchRowCount":12,"fetchRowCountMax":1,"fetchRowHistogram":[0,12]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":6,"executeMillisMax":2,"executeMillisTotal":8,"executeHistogram":[3,3],"executeAndResultHoldHistogram":[0,6],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":1,"fetchRowHistogram":[0,6]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":6,"executeMillisMax":2,"executeMillisTotal":8,"executeHistogram":[1,5],"executeAndResultHoldHistogram":[0,6],"concurrentMax":1,"fetchRowCount":74,"fetchRowCountMax":17,"fetchRowHistogram":[0,0,6],"clobOpenCount":74}]}
[1;35m10:34:36.667[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:34:36.675[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102311分钟
[1;35m10:34:36.678[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:34:54.952[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":4,"connectCount":0,"closeCount":0}
[1;35m10:34:56.462[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:34:07","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:34:07","connectCount":22,"closeCount":22,"executeCount":22,"pstmtCacheHitCount":21,"pstmtCacheMissCount":1,"connectionHoldTimeHistogram":[0,22],"clobOpenCount":64,"sqlList":[{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":11,"executeMillisMax":5,"executeMillisTotal":35,"executeHistogram":[0,11],"executeAndResultHoldHistogram":[0,11],"concurrentMax":1,"fetchRowCount":11,"fetchRowCountMax":1,"fetchRowHistogram":[0,11]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":5,"executeMillisMax":2,"executeMillisTotal":5,"executeHistogram":[3,2],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"fetchRowCount":5,"fetchRowCountMax":1,"fetchRowHistogram":[0,5]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":5,"executeMillisMax":2,"executeMillisTotal":7,"executeHistogram":[2,3],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"fetchRowCount":64,"fetchRowCountMax":17,"fetchRowHistogram":[0,0,5],"clobOpenCount":64},{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:35:36.668[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:35:36.677[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102313分钟
[1;35m10:35:36.680[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:35:54.953[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":4,"connectCount":0,"closeCount":0}
[1;35m10:35:56.465[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:36:36.667[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:36:36.701[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102315分钟
[1;35m10:36:36.713[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:36:54.962[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":4,"connectCount":0,"closeCount":0}
[1;35m10:36:56.466[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:37:36.676[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_server_info' on lock
[1;35m10:37:36.676[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:37:36.681[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.l.i.ProductLicenseInfoServiceImpl[0;39m - [36m[printDeploymentLog,578][0;39m - LICENSE SERVER INFO  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
[1;35m10:37:36.702[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102317分钟
[1;35m10:37:36.707[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:37:46.496[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Directory info : D:\x
[1;35m10:37:46.496[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk usage : 10180005680
[1;35m10:37:46.496[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk info : Drive > D:\    Total space > 70179434496    Free space > 21406556160
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Network info : {eth5={hostname=DESKTOP-38F0Q6A, ip=**********, network-arch=Realtek Gaming GbE Family Controller, ipnet=ipv4, mac=18-C0-4D-EE-B7-42}}
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU ID : null
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU core : **********
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System : Windows 11
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System version : 10.0
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System architecture: amd64
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System hostname : DESKTOP-38F0Q6A
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Total memory : 51397271552
[1;35m10:37:46.497[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - User Info : [must]
companyNameZh=优锘
mmdID=uino
validityPeriod=development
[other]
companyNameEn=uino
releaseTime=************
[1;35m10:37:46.500[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_server_info' is unlocked
[1;35m10:37:54.963[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0,"physicalCloseCount":1}
[1;35m10:37:56.468[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:37:56.771[0;39m [32m[nacos-grpc-client-executor-***********-134][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Receive server push request, request = NotifySubscriberRequest, requestId = 7213
[1;35m10:37:56.772[0;39m [32m[nacos-grpc-client-executor-***********-134][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,107][0;39m - removed ips(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:37:56.773[0;39m [32m[nacos-grpc-client-executor-***********-134][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:37:56.782[0;39m [32m[nacos-grpc-client-executor-***********-134][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Ack server push request, request = NotifySubscriberRequest, requestId = 7213
[1;35m10:38:36.667[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:38:36.713[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102319分钟
[1;35m10:38:36.719[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:38:54.964[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:38:56.469[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:37:58","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:37:58","connectCount":22,"closeCount":22,"executeCount":22,"pstmtCacheHitCount":22,"connectionHoldTimeHistogram":[0,22],"clobOpenCount":64,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":11,"executeMillisMax":5,"executeMillisTotal":38,"executeHistogram":[0,11],"executeAndResultHoldHistogram":[0,11],"concurrentMax":1,"fetchRowCount":11,"fetchRowCountMax":1,"fetchRowHistogram":[0,11]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":5,"executeMillisMax":1,"executeMillisTotal":5,"executeHistogram":[2,3],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"fetchRowCount":5,"fetchRowCountMax":1,"fetchRowHistogram":[0,5]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":5,"executeMillisMax":2,"executeMillisTotal":7,"executeHistogram":[0,5],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"fetchRowCount":64,"fetchRowCountMax":17,"fetchRowHistogram":[0,0,5],"clobOpenCount":64}]}
[1;35m10:39:36.668[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:39:36.678[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102321分钟
[1;35m10:39:36.681[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:39:43.927[0;39m [32m[http-nio-10004-exec-8][0;39m [34mINFO [0;39m [36mo.a.t.u.h.p.Cookie[0;39m - [36m[log,168][0;39m - A cookie header was received [Hm_lvt_856d7b968407d0887b51e18fed5d338e=**********,**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
[1;35m10:39:43.943[0;39m [32m[http-nio-10004-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - TenantInfoController#getMultiTenantEnable proceed time: 3ms
[1;35m10:39:43.948[0;39m [32m[http-nio-10004-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - log-manager-factory executor is create!
[1;35m10:39:44.141[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m10:39:44.141[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m10:39:44.150[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:39:44.151[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:39:44.205[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:39:44.249[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 7ms
[1;35m10:39:44.291[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:39:44.395[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 11ms
[1;35m10:39:44.429[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:44.440[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 12ms
[1;35m10:39:44.487[0;39m [32m[http-nio-10004-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 4ms
[1;35m10:39:44.522[0;39m [32m[http-nio-10004-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysMenuController#list proceed time: 128ms
[1;35m10:39:44.670[0;39m [32m[http-nio-10004-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 5ms
[1;35m10:39:44.677[0;39m [32m[nacos-grpc-client-executor-***********-177][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Receive server push request, request = NotifySubscriberRequest, requestId = 7226
[1;35m10:39:44.679[0;39m [32m[nacos-grpc-client-executor-***********-177][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [51dea487-3136-46fb-978b-5a370d792ee9] Ack server push request, request = NotifySubscriberRequest, requestId = 7226
[1;35m10:39:44.684[0;39m [32m[http-nio-10004-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysMenuController#change proceed time: 44ms
[1;35m10:39:45.400[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysOpLogController#opLogRecord proceed time: 66ms
[1;35m10:39:45.455[0;39m [32m[http-nio-10004-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - RealTimeMessageController#pageMessage proceed time: 90ms
[1;35m10:39:54.964[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:39:56.471[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"activePeak":2,"activePeakTime":"2025-08-25 10:39:44","poolingCount":2,"poolingPeak":2,"poolingPeakTime":"2025-08-25 10:39:44","connectCount":432,"closeCount":432,"notEmptyWaitCount":1,"notEmptyWaitMillis":14,"physicalConnectCount":1,"executeCount":432,"pstmtCacheHitCount":414,"pstmtCacheMissCount":18,"connectionHoldTimeHistogram":[0,421,11],"clobOpenCount":1995,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":2,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[1,1],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":216,"executeMillisMax":14,"executeMillisTotal":760,"executeHistogram":[0,212,4],"executeAndResultHoldHistogram":[0,212,4],"concurrentMax":1,"fetchRowCount":216,"fetchRowCountMax":1,"fetchRowHistogram":[0,216]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":30,"executeMillisMax":7,"executeMillisTotal":63,"executeHistogram":[11,19],"executeAndResultHoldHistogram":[0,30],"concurrentMax":1,"fetchRowCount":30,"fetchRowCountMax":1,"fetchRowHistogram":[0,30]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":82,"executeMillisMax":7,"executeMillisTotal":118,"executeHistogram":[45,37],"executeAndResultHoldHistogram":[11,71],"concurrentMax":1,"fetchRowCount":82,"fetchRowCountMax":1,"fetchRowHistogram":[0,82]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":82,"executeMillisMax":8,"executeMillisTotal":144,"executeHistogram":[14,68],"executeAndResultHoldHistogram":[0,82],"concurrentMax":1,"fetchRowCount":1031,"fetchRowCountMax":17,"fetchRowHistogram":[0,2,80],"clobOpenCount":1031},{"sql":"SELECT id,job_num,dept_id,dept_name,status,create_time,create_user,update_time,update_user FROM sys_user_detail WHERE id=?","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT  id,user_id,role_id,status,create_time,create_user,update_time,update_user  FROM sys_user_role      WHERE  (user_id = ?)","executeCount":3,"executeMillisMax":4,"executeMillisTotal":7,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT  id,name,code,sort,data_scope_type,remark,sys_category_id,status,default_flag,create_time,create_user,update_time,update_user  FROM sys_role      WHERE  (id IN (?) AND status = ?)","executeCount":2,"executeMillisMax":2,"executeMillisTotal":3,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT  id,role_id,menu_id,status,create_time,create_user,update_time,update_user  FROM sys_role_menu      WHERE  (role_id IN (?))","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":792,"fetchRowCountMax":792,"fetchRowHistogram":[0,0,0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND type = ? AND status = ? AND application <> ?)","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":405,"fetchRowCountMax":405,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":405},{"sql":"SELECT id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user FROM sys_user WHERE id=?","executeCount":3,"executeMillisMax":0,"executeMillisTotal":2,"executeHistogram":[3],"executeAndResultHoldHistogram":[1,2],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT  id,name,icon,code,active,sort,status,type,plugin_id,sys_category_id,micro_app_id,visible,link,iframe_type,create_time,create_user,update_time,update_user  FROM sys_app      WHERE  (status = ? AND sort <> ? AND code <> ?) ORDER BY sort ASC","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":6,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (status = ? AND application = ? AND type NOT IN (?) AND weight NOT IN (?)) ORDER BY sort ASC","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":25,"fetchRowCountMax":24,"fetchRowHistogram":[0,1,1],"clobOpenCount":25},{"sql":"SELECT  id,user_id,theme_json,status,create_time,create_user,update_time,update_user  FROM user_settings      WHERE  (user_id = ?)","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1},{"sql":"select value\n        from sys_config\n        where code in\n         (  \n\n            ?\n         )","executeCount":3,"executeMillisMax":0,"executeMillisTotal":2,"executeHistogram":[3],"executeAndResultHoldHistogram":[1,2],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3],"clobOpenCount":3},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (status = ? AND application <> ?) ORDER BY sort ASC,create_time ASC","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":500,"fetchRowCountMax":500,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":500},{"sql":"SELECT  id,name,icon,code,active,sort,status,type,plugin_id,sys_category_id,micro_app_id,visible,link,iframe_type,create_time,create_user,update_time,update_user  FROM sys_app","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":6,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,name,code,icon,integration_mode,url,version,run_time,status,create_time,create_user,update_time,update_user  FROM sys_micro_app","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1},{"sql":"SELECT  id,message,attachment_address,status,create_time,create_user,update_time,update_user  FROM sys_real_time_message      WHERE  (create_user = ?) ORDER BY create_time DESC","executeCount":1,"executeMillisMax":4,"executeMillisTotal":4,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":31,"fetchRowCountMax":31,"fetchRowHistogram":[0,0,1],"clobOpenCount":31}]}
[1;35m10:40:00.665[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:00.666[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - tenant=XQJII
[1;35m10:40:05.200[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:05.201[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJYUUpJSTpkYXRhU3luYyIsInJuU3RyIjoiZWJiOHMwVDVyT24wVGpNV2tjeWRGaW1OUTN0cTF1TDYiLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.EpRViWMGAhqKHvqdzIToORnqLlbSuywi1NTieXVhB0E
[1;35m10:40:20.787[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:20.787[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - type=message
[1;35m10:40:25.447[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:25.447[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - tenant=XQJII
[1;35m10:40:36.674[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:40:36.683[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102323分钟
[1;35m10:40:36.686[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:40:38.206[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - TenantInfoController#getMultiTenantEnable proceed time: 0ms
[1;35m10:40:38.417[0;39m [32m[http-nio-10004-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 7ms
[1;35m10:40:38.614[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 9ms
[1;35m10:40:38.720[0;39m [32m[http-nio-10004-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 7ms
[1;35m10:40:38.728[0;39m [32m[http-nio-10004-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysMenuController#list proceed time: 107ms
[1;35m10:40:38.791[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 9ms
[1;35m10:40:38.911[0;39m [32m[http-nio-10004-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 21ms
[1;35m10:40:38.930[0;39m [32m[http-nio-10004-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysMenuController#change proceed time: 84ms
[1;35m10:40:39.275[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - RealTimeMessageController#pageMessage proceed time: 36ms
[1;35m10:40:39.289[0;39m [32m[http-nio-10004-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysOpLogController#opLogRecord proceed time: 45ms
[1;35m10:40:41.034[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:41.035[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJYUUpJSTpkYXRhU3luYyIsInJuU3RyIjoiZWJiOHMwVDVyT24wVGpNV2tjeWRGaW1OUTN0cTF1TDYiLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.EpRViWMGAhqKHvqdzIToORnqLlbSuywi1NTieXVhB0E
[1;35m10:40:43.640[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:40:43.640[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - tenant=XQJII
[1;35m10:40:45.562[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:45.562[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - type=message
[1;35m10:40:45.568[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.s.CustomWebSocketChannelHandler[0;39m - [36m[buildId,171][0;39m - id: message:XQJII:1
[1;35m10:40:45.569[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[afterHandshake,114][0;39m - 握手完成,id: message:XQJII:1
[1;35m10:40:54.971[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:40:56.474[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"activePeak":2,"activePeakTime":"2025-08-25 10:40:38","poolingCount":2,"poolingPeak":2,"poolingPeakTime":"2025-08-25 10:39:57","connectCount":212,"closeCount":212,"executeCount":212,"pstmtCacheHitCount":205,"pstmtCacheMissCount":7,"connectionHoldTimeHistogram":[0,202,10],"clobOpenCount":1355,"sqlList":[{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":106,"executeMillisMax":16,"executeMillisTotal":397,"executeHistogram":[0,104,2],"executeAndResultHoldHistogram":[0,104,2],"concurrentMax":1,"fetchRowCount":106,"fetchRowCountMax":1,"fetchRowHistogram":[0,106]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":20,"executeMillisMax":17,"executeMillisTotal":71,"executeHistogram":[3,15,2],"executeAndResultHoldHistogram":[0,18,2],"concurrentMax":1,"fetchRowCount":20,"fetchRowCountMax":1,"fetchRowHistogram":[0,20]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":32,"executeMillisMax":10,"executeMillisTotal":35,"executeHistogram":[27,4,1],"executeAndResultHoldHistogram":[11,20,1],"concurrentMax":1,"fetchRowCount":32,"fetchRowCountMax":1,"fetchRowHistogram":[0,32]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":32,"executeMillisMax":3,"executeMillisTotal":39,"executeHistogram":[19,13],"executeAndResultHoldHistogram":[1,31],"concurrentMax":1,"fetchRowCount":391,"fetchRowCountMax":17,"fetchRowHistogram":[0,2,30],"clobOpenCount":391},{"sql":"SELECT id,job_num,dept_id,dept_name,status,create_time,create_user,update_time,update_user FROM sys_user_detail WHERE id=?","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT  id,user_id,role_id,status,create_time,create_user,update_time,update_user  FROM sys_user_role      WHERE  (user_id = ?)","executeCount":3,"executeMillisMax":1,"executeMillisTotal":3,"executeHistogram":[2,1],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT  id,name,code,sort,data_scope_type,remark,sys_category_id,status,default_flag,create_time,create_user,update_time,update_user  FROM sys_role      WHERE  (id IN (?) AND status = ?)","executeCount":2,"executeMillisMax":6,"executeMillisTotal":7,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT  id,role_id,menu_id,status,create_time,create_user,update_time,update_user  FROM sys_role_menu      WHERE  (role_id IN (?))","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":792,"fetchRowCountMax":792,"fetchRowHistogram":[0,0,0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND type = ? AND status = ? AND application <> ?)","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":405,"fetchRowCountMax":405,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":405},{"sql":"SELECT id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user FROM sys_user WHERE id=?","executeCount":3,"executeMillisMax":5,"executeMillisTotal":10,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT  id,name,icon,code,active,sort,status,type,plugin_id,sys_category_id,micro_app_id,visible,link,iframe_type,create_time,create_user,update_time,update_user  FROM sys_app      WHERE  (status = ? AND sort <> ? AND code <> ?) ORDER BY sort ASC","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":6,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (status = ? AND application = ? AND type NOT IN (?) AND weight NOT IN (?)) ORDER BY sort ASC","executeCount":2,"executeMillisMax":3,"executeMillisTotal":4,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":25,"fetchRowCountMax":24,"fetchRowHistogram":[0,1,1],"clobOpenCount":25},{"sql":"SELECT  id,user_id,theme_json,status,create_time,create_user,update_time,update_user  FROM user_settings      WHERE  (user_id = ?)","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1},{"sql":"select value\n        from sys_config\n        where code in\n         (  \n\n            ?\n         )","executeCount":3,"executeMillisMax":2,"executeMillisTotal":4,"executeHistogram":[1,2],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3],"clobOpenCount":3},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (status = ? AND application <> ?) ORDER BY sort ASC,create_time ASC","executeCount":1,"executeMillisMax":7,"executeMillisTotal":7,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":500,"fetchRowCountMax":500,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":500},{"sql":"SELECT  id,name,icon,code,active,sort,status,type,plugin_id,sys_category_id,micro_app_id,visible,link,iframe_type,create_time,create_user,update_time,update_user  FROM sys_app","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":6,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,name,code,icon,integration_mode,url,version,run_time,status,create_time,create_user,update_time,update_user  FROM sys_micro_app","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT  id,message,attachment_address,status,create_time,create_user,update_time,update_user  FROM sys_real_time_message      WHERE  (create_user = ?) ORDER BY create_time DESC","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":31,"fetchRowCountMax":31,"fetchRowHistogram":[0,0,1],"clobOpenCount":31}]}
[1;35m10:40:59.232[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:59.232[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:40:59.232[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJYUUpJSTpkYXRhU3luYyIsInJuU3RyIjoiZWJiOHMwVDVyT24wVGpNV2tjeWRGaW1OUTN0cTF1TDYiLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.EpRViWMGAhqKHvqdzIToORnqLlbSuywi1NTieXVhB0E
[1;35m10:40:59.233[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - tenant=XQJII
[1;35m10:40:59.233[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:59.233[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJYUUpJSTpkYXRhU3luYyIsInJuU3RyIjoiZWJiOHMwVDVyT24wVGpNV2tjeWRGaW1OUTN0cTF1TDYiLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.EpRViWMGAhqKHvqdzIToORnqLlbSuywi1NTieXVhB0E
[1;35m10:40:59.233[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:59.234[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - type=message
[1;35m10:40:59.237[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.s.CustomWebSocketChannelHandler[0;39m - [36m[buildId,171][0;39m - id: message:XQJII:1
[1;35m10:40:59.237[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[afterConnectionEstablished,126][0;39m - 连接完成,id: message:XQJII:1
[1;35m10:40:59.248[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:59.248[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - tenant=XQJII
[1;35m10:40:59.248[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:59.248[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJYUUpJSTpkYXRhU3luYyIsInJuU3RyIjoiZWJiOHMwVDVyT24wVGpNV2tjeWRGaW1OUTN0cTF1TDYiLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.EpRViWMGAhqKHvqdzIToORnqLlbSuywi1NTieXVhB0E
[1;35m10:40:59.248[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60032
[1;35m10:40:59.248[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - type=message
[1;35m10:40:59.252[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.s.CustomWebSocketChannelHandler[0;39m - [36m[buildId,171][0;39m - id: message:XQJII:1
[1;35m10:40:59.252[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[afterConnectionClosed,151][0;39m - 已关闭连接,id: message:XQJII:1
[1;35m10:41:14.827[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:41:14.827[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - type=message
[1;35m10:41:19.346[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:41:19.346[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - tenant=XQJII
[1;35m10:41:34.936[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:41:34.936[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJYUUpJSTpkYXRhU3luYyIsInJuU3RyIjoiZWJiOHMwVDVyT24wVGpNV2tjeWRGaW1OUTN0cTF1TDYiLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.EpRViWMGAhqKHvqdzIToORnqLlbSuywi1NTieXVhB0E
[1;35m10:41:36.668[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:41:36.675[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102325分钟
[1;35m10:41:36.677[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:41:39.466[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:41:39.466[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - type=message
[1;35m10:41:39.471[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.s.CustomWebSocketChannelHandler[0;39m - [36m[buildId,171][0;39m - id: message:XQJII:1
[1;35m10:41:39.471[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[afterHandshake,114][0;39m - 握手完成,id: message:XQJII:1
[1;35m10:41:54.980[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:41:55.100[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:41:55.100[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - tenant=XQJII
[1;35m10:41:55.101[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:41:55.101[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJYUUpJSTpkYXRhU3luYyIsInJuU3RyIjoiZWJiOHMwVDVyT24wVGpNV2tjeWRGaW1OUTN0cTF1TDYiLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.EpRViWMGAhqKHvqdzIToORnqLlbSuywi1NTieXVhB0E
[1;35m10:41:55.101[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,184][0;39m - Host:***********, Remote address: ***********/***********:60082
[1;35m10:41:55.101[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromSession,200][0;39m - type=message
[1;35m10:41:55.104[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.s.CustomWebSocketChannelHandler[0;39m - [36m[buildId,171][0;39m - id: message:XQJII:1
[1;35m10:41:55.105[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[afterConnectionEstablished,126][0;39m - 连接完成,id: message:XQJII:1
[1;35m10:41:56.478[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:41:44","poolingCount":2,"poolingPeak":2,"poolingPeakTime":"2025-08-25 10:41:44","connectCount":22,"closeCount":22,"executeCount":22,"pstmtCacheHitCount":22,"connectionHoldTimeHistogram":[0,21,1],"clobOpenCount":64,"sqlList":[{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":11,"executeMillisMax":12,"executeMillisTotal":38,"executeHistogram":[0,10,1],"executeAndResultHoldHistogram":[0,10,1],"concurrentMax":1,"fetchRowCount":11,"fetchRowCountMax":1,"fetchRowHistogram":[0,11]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":5,"executeMillisMax":0,"executeMillisTotal":3,"executeHistogram":[5],"executeAndResultHoldHistogram":[2,3],"concurrentMax":1,"fetchRowCount":5,"fetchRowCountMax":1,"fetchRowHistogram":[0,5]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":5,"executeMillisMax":1,"executeMillisTotal":5,"executeHistogram":[1,4],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"fetchRowCount":64,"fetchRowCountMax":17,"fetchRowHistogram":[0,0,5],"clobOpenCount":64},{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":4,"executeMillisTotal":4,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:42:36.667[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:42:36.667[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_server_info' on lock
[1;35m10:42:36.672[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.l.i.ProductLicenseInfoServiceImpl[0;39m - [36m[printDeploymentLog,578][0;39m - LICENSE SERVER INFO  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
[1;35m10:42:36.676[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102327分钟
[1;35m10:42:36.678[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:42:45.558[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Directory info : D:\x
[1;35m10:42:45.558[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk usage : 10180517056
[1;35m10:42:45.558[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk info : Drive > D:\    Total space > 70179434496    Free space > 21396676608
[1;35m10:42:45.558[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Network info : {eth5={hostname=DESKTOP-38F0Q6A, ip=**********, network-arch=Realtek Gaming GbE Family Controller, ipnet=ipv4, mac=18-C0-4D-EE-B7-42}}
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU ID : null
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU core : **********
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System : Windows 11
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System version : 10.0
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System architecture: amd64
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System hostname : DESKTOP-38F0Q6A
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Total memory : 51397271552
[1;35m10:42:45.559[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - User Info : [must]
companyNameZh=优锘
mmdID=uino
validityPeriod=development
[other]
companyNameEn=uino
releaseTime=************
[1;35m10:42:45.562[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_server_info' is unlocked
[1;35m10:42:54.980[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:42:56.484[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"poolingCount":2,"connectCount":0,"closeCount":0}
[1;35m10:43:36.667[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:43:36.675[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102329分钟
[1;35m10:43:36.678[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:43:54.981[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:43:56.485[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"poolingCount":2,"connectCount":0,"closeCount":0}
[1;35m10:44:36.672[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:44:36.679[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102331分钟
[1;35m10:44:36.681[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m10:44:54.982[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:44:56.487[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806089290895361","activeCount":0,"poolingCount":2,"connectCount":0,"closeCount":0}
[1;35m10:45:36.675[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m10:45:36.682[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 102333分钟
[1;35m10:45:36.690[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
