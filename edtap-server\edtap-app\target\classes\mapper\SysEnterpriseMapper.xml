<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.uino.x.edtap.dao.enterprise.SysEnterpriseMapper">
    <select id="selectPageByLatest" resultType="com.uino.x.edtap.pojo.enterprise.vo.SysEnterpriseVo"
            parameterType="com.uino.x.edtap.dao.enterprise.SysEnterpriseMapper">
        select se.id,se.name,se.address,se.website,se.logo,se.remark,se.contacts,
        se.developer,se.developer_phone,
        se.project_max_count,se.user_max_count,
        se.STATUS,se.register_time,se.create_time,se.create_user,se.update_time,se.update_user,
        MAX(sue.acquiesce_flag) as acquiesce_flag
        from sys_enterprise se
            left join sys_user_enterprise sue on se.id = sue.enterprise_id AND sue.status != 2
        <where>
            se.`status` != 2
            <if test="name != null and name != ''">
                and se.`name` like concat('%',#{name},'%')
            </if>
            <if test="userId != null and userId != ''">
                and sue.`user_id` = #{userId}
            </if>
        </where>
        GROUP BY se.id,se.name,se.address,se.website,se.logo,se.remark,se.contacts,
        se.developer,se.developer_phone,
        se.project_max_count,se.user_max_count,
        se.STATUS,se.register_time,se.create_time,se.create_user,se.update_time,se.update_user
        <choose>
            <when test="sortField != null and sortField != ''">
                <if test="sortRule == 'DESC'">
                    order by se.${sortField} DESC
                </if>
                <if test="sortRule == 'ASC'">
                    order by se.${sortField} ASC
                </if>
            </when>
            <otherwise>
                order by se.STATUS ASC,se.create_time DESC
            </otherwise>
        </choose>
        limit #{pageFrom},#{pageEnd}
    </select>
    <select id="selectByUserId" resultType="com.uino.x.edtap.pojo.enterprise.vo.SysEnterpriseVo">
        select se.id,se.name,se.address,se.website,se.logo,se.remark,se.contacts,
               se.developer,se.developer_phone,
               se.project_max_count,se.user_max_count,
               se.STATUS,se.register_time,se.create_time,se.create_user,se.update_time,se.update_user,sue.acquiesce_flag
        from sys_enterprise se left join sys_user_enterprise sue on se.id = sue.enterprise_id
        where se.`status` = 0 and sue.`status` in (0,3) and sue.user_id = #{userId}
        GROUP BY
            se.id,se.name,se.address,se.website,se.logo,se.remark,se.contacts,
            se.developer,se.developer_phone,
            se.project_max_count,se.user_max_count,
            se.STATUS,se.register_time,se.create_time,se.create_user,se.update_time,se.update_user,sue.acquiesce_flag
        order by se.STATUS ASC,se.create_time DESC
    </select>
</mapper>
