[1;35m16:37:46.365[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m16:37:46.461[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m16:37:46.618[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m16:37:46.733[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m16:37:46.745[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStarting,53][0;39m - Starting SystemXApplication using Java 17.0.14 with PID 25156 (D:\x\pedestal-x\pedestal-x-app\system-x\target\classes started by Administrator in D:\x)
[1;35m16:37:46.751[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal"
[1;35m16:37:46.870[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m16:37:46.871[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m16:37:46.871[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=system-x.yml, group=DEFAULT_GROUP] success
[1;35m16:37:51.810[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'microAppFallbackFactory' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=microAppApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/microapp/api/config/MicroAppApiConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=projectPackApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/projectpack/api/config/ProjectPackApiConfig.class]]
[1;35m16:37:52.028[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.030[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.032[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.034[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.035[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.037[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.039[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.041[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.043[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.046[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.049[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.053[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.056[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.058[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.061[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.064[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.066[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.069[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.073[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.076[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.080[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.083[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.086[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.087[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.089[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.090[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.092[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.092[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.094[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.095[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.096[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.097[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.098[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.098[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.099[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.101[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.101[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.103[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.105[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.107[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.109[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.112[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.annotate:annotate}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.115[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.file:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.116[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.117[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.118[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.119[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.121[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.log:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.124[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.tenant:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.127[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.bubble:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.129[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.config:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.141[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.timer:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.145[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.effectpackage:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.150[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.viewpoint:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m16:37:52.567[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'paginationInnerInterceptor' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=mybatisConfig; factoryMethodName=paginationInnerInterceptor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/app/systemx/config/MybatisConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.uino.x.common.dameng.CommonXDmConfig; factoryMethodName=paginationInnerInterceptor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/common/dameng/CommonXDmConfig.class]]
[1;35m16:37:53.490[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m16:37:53.509[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[1;35m16:37:53.626[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 68 ms. Found 0 Elasticsearch repository interfaces.
[1;35m16:37:53.644[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m16:37:53.647[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[1;35m16:37:53.680[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 27 ms. Found 0 Reactive Elasticsearch repository interfaces.
[1;35m16:37:53.711[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m16:37:53.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m16:37:53.759[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
[1;35m16:37:54.892[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=7c093ee1-8259-3a0a-bbd8-0ac350c6c415
[1;35m16:37:55.017[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.s.SqlTransformationStrategyFactory[0;39m - [36m[createReplaceIntoStrategy,215][0;39m - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
[1;35m16:37:55.025[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.s.SqlTransformationStrategyFactory[0;39m - [36m[createDefaultStrategies,103][0;39m - Created 9 default transformation strategies
[1;35m16:37:55.036[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[logStrategyRegistration,625][0;39m - Registered 9 transformation strategies in engine [dm]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, BacktickQuoteTransformationStrategy]
[1;35m16:37:55.037[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[<init>,235][0;39m - SQL transformation engine [dm] initialized with 9 strategies
[1;35m16:37:55.111[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-1} inited
[1;35m16:37:55.267[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,346][0;39m - SQL transformation statistics: 
SqlTransformationEngine [dm] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 9 registered
  Memory: Estimated Size=0 bytes
[1;35m16:37:55.371[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:37:55.432[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-1} closing ...
[1;35m16:37:55.436[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-1} closed
[1;35m16:37:59.954[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 10004 (http)
[1;35m16:38:00.049[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-10004"]
[1;35m16:38:00.058[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m16:38:00.059[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.43]
[1;35m16:38:00.332[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m16:38:00.333[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 13456 ms
[1;35m16:38:05.102[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m16:38:07.876[0;39m [32m[redisson-netty-1-4][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m16:38:07.901[0;39m [32m[redisson-netty-1-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m16:38:08.570[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m16:38:08.605[0;39m [32m[redisson-netty-5-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m16:38:08.630[0;39m [32m[redisson-netty-5-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m16:38:09.445[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [systemx] success
[1;35m16:38:09.445[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[afterPropertiesSet,241][0;39m - dynamic-datasource initial loaded [1] datasource,primary datasource named [systemx]
[1;35m16:38:09.619[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-2,systemx} inited
[1;35m16:38:09.623[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-22 16:38:09","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m16:38:12.756[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m16:38:12.973[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.195s)
[1;35m16:38:13.013[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "systemx": 2023.11.15.09.00.00
[1;35m16:38:13.018[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "systemx" is up to date. No migration necessary.
[1;35m16:38:13.869[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:38:14.105[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - micro-app-service-impl executor is create!
[1;35m16:38:14.862[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - tenant-info-service-impl executor is create!
[1;35m16:38:15.340[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,781][0;39m - Authorization initialized
[1;35m16:38:15.340[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,782][0;39m - License status : Disabled
[1;35m16:38:59.021[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Directory info : D:\x
[1;35m16:38:59.021[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk usage : 10179718949
[1;35m16:38:59.021[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk info : Drive > D:\    Total space > 70179434496    Free space > 21349412864
[1;35m16:38:59.022[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Network info : {eth5={hostname=DESKTOP-38F0Q6A, ip=**********, network-arch=Realtek Gaming GbE Family Controller, ipnet=ipv4, mac=18-C0-4D-EE-B7-42}}
[1;35m16:38:59.022[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU ID : null
[1;35m16:38:59.022[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU core : **********
[1;35m16:38:59.022[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System : Windows 11
[1;35m16:38:59.023[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System version : 10.0
[1;35m16:38:59.023[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System architecture: amd64
[1;35m16:38:59.023[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System hostname : DESKTOP-38F0Q6A
[1;35m16:38:59.023[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Total memory : 51397271552
[1;35m16:38:59.023[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - User Info : [must]
companyNameZh=优锘
mmdID=uino
validityPeriod=development
[other]
companyNameEn=uino
releaseTime=231114161431
[1;35m16:38:59.121[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.l.i.ProductLicenseInfoServiceImpl[0;39m - [36m[<clinit>,145][0;39m - 当前机器标识: **********,**********
[1;35m16:38:59.610[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:00.376[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - sys-user-service-impl executor is create!
[1;35m16:39:01.006[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:01.892[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:01.983[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:02.010[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - device-alert-service-impl executor is create!
[1;35m16:39:02.269[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - sys-dict-data-service-impl executor is create!
[1;35m16:39:02.622[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'distribution' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.614[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.647[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.661[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.678[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.723[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.739[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.748[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.889[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:03.917[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - sys-bubble-info-service-impl executor is create!
[1;35m16:39:05.630[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'log-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:05.738[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - device-performance-service-impl executor is create!
[1;35m16:39:05.822[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:05.862[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:05.875[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:06.085[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:06.175[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - project-pack-service-impl executor is create!
[1;35m16:39:06.387[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'edtap-server' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:06.399[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - data-migration-service-impl executor is create!
[1;35m16:39:06.447[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'ti-dix' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:39:07.999[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.w.s.f.ReflectionServiceFactoryBean[0;39m - [36m[buildServiceFromClass,436][0;39m - Creating Service {http://provisioning.idm.shenhua.com}ProvisioningWSService from class com.uino.x.pedestal.app.systemx.webservice.ProvisioningWSServicePortType
[1;35m16:39:09.412[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.e.ServerImpl[0;39m - [36m[initDestination,95][0;39m - Setting the server's publish address to be /ProvisioningService
[1;35m16:39:09.619[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m16:39:09.626[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"activePeak":3,"activePeakTime":"2025-08-22 16:38:13","poolingCount":2,"poolingPeak":2,"poolingPeakTime":"2025-08-22 16:38:13","connectCount":3,"closeCount":2,"notEmptyWaitCount":2,"notEmptyWaitMillis":25,"physicalConnectCount":2,"executeCount":12,"commitCount":5,"pstmtCacheHitCount":4,"pstmtCacheMissCount":8,"startTransactionCount":5,"transactionHistogram":[0,4,1],"connectionHoldTimeHistogram":[0,0,1,1],"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":2,"executeMillisMax":0,"executeMillisTotal":1,"executeHistogram":[2],"executeAndResultHoldHistogram":[2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') FROM DUAL","executeCount":4,"executeMillisMax":0,"executeMillisTotal":2,"executeHistogram":[4],"executeAndResultHoldHistogram":[4],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT USER FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT CASE WHEN EXISTS(SELECT DISTINCT OBJECT_NAME FROM ALL_OBJECTS WHERE OBJECT_TYPE = 'SCH' AND OBJECT_NAME = ?) THEN 1 ELSE 0 END FROM DUAL","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2],"inTransactionCount":1},{"sql":"SELECT \"installed_rank\",\"version\",\"description\",\"type\",\"script\",\"checksum\",\"installed_on\",\"installed_by\",\"execution_time\",\"success\" FROM \"systemx\".\"flyway_schema_history\" WHERE \"installed_rank\" > ? ORDER BY \"installed_rank\"","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[1,1],"concurrentMax":1,"fetchRowCount":25,"fetchRowCountMax":25,"fetchRowHistogram":[1,0,1],"inTransactionCount":2},{"sql":"LOCK TABLE \"systemx\".\"flyway_schema_history\" IN EXCLUSIVE MODE","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"inTransactionCount":1},{"sql":"ALTER SESSION SET CURRENT_SCHEMA=\"systemx\"","executeCount":2,"executeMillisMax":0,"executeMillisTotal":1,"executeHistogram":[2],"executeAndResultHoldHistogram":[2],"concurrentMax":1,"inTransactionCount":2}]}
[1;35m16:39:09.665[0;39m [32m[redisson-netty-8-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m16:39:09.715[0;39m [32m[redisson-netty-8-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m16:39:10.019[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.WebMvcConfig[0;39m - [36m[addInterceptors,55][0;39m - 输出白名单: [/favicon.ico, /v3/api-docs/**, /doc.html, /webjars/**, /webSocket, /sys-user/get-user-by-account, /sys-user/update-by-id, /sys-user/save-by-default, /sys-role/get-role-code-list, /sys-menu/get-login-permissions, /sys-user/get-sync-user-list, /sys-user/find-ldap-user-by-account, /sys-user-detail/get-sync-user-detail-list, /sys-dept/get-sync-dept-list, /tenant-info/get-sync-tenant-list, /tenant-info/get-multi-tenant-enable, /tenant-info/sync-app-menu, /sys-identity/ldap, /sys-identity/find-enable-ldap, /sys-identity/add-ldap-user, /sys-identity/check-forbid-ip, /web-assets/xxv-web-assets-list, /sys-config/load-xxv-configs, /sys-file-info/get-bucket-value-name, /tenant-info/feign-add, /tenant-info/feign-edit, /tenant-info/feign-delete, /tenant-info/get-sync-tenant-list1, /tenant-info/feign-edit1, /tenant-info/delete, /sys-user/update-pwd, /sys-user/update-project-pwd, /web-assets/sync, /data-migration/create-database, /data-migration/upload-database-info, /data-migration/feign-download-database-info, /data-migration/download-database-info, /data-migration/feign-download-file, /data-migration/do-export-data, /data-migration/download-file, /tenant-info/get-tenant-by-code, /druid/**, /druid/index.html, /project-pack/detail-pack/**, /sys-config/open, /license-info/validate-license, /license-info/get-license-info, /license-info/upload-license, /license-info/thing-js-api, /license-info/thing-js-2-api, /license-info/get-master-license-info, /auth/**, /sync/**]
[1;35m16:39:10.022[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.SentinelWebMvcConfigurer[0;39m - [36m[addInterceptors,52][0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[1;35m16:39:11.557[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.m.e.s.MybatisPlusApplicationContextAware[0;39m - [36m[setApplicationContext,40][0;39m - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2b73bd6b
[1;35m16:39:13.839[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 1 endpoint beneath base path '/actuator'
[1;35m16:39:14.576[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Starting ProtocolHandler ["http-nio-10004"]
[1;35m16:39:14.604[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[start,243][0;39m - Tomcat started on port 10004 (http) with context path '/'
[1;35m16:39:14.611[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[init,102][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m16:39:14.612[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[initNamespaceForNaming,62][0;39m - initializer namespace from ans.namespace attribute : null
[1;35m16:39:14.612[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$0,66][0;39m - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[1;35m16:39:14.613[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$1,73][0;39m - initializer namespace from namespace attribute :null
[1;35m16:39:14.622[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,75][0;39m - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[1;35m16:39:14.626[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m16:39:14.627[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m16:39:14.748[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833
[1;35m16:39:14.751[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,109][0;39m - Create naming rpc client for uuid->ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833
[1;35m16:39:14.751[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[1;35m16:39:14.752[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[1;35m16:39:14.753[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[1;35m16:39:14.753[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m16:39:14.753[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m16:39:14.775[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Success to connect to server [***********:8848] on start up, connectionId = 1755851953874_**********_60828
[1;35m16:39:14.776[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m16:39:14.776[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Notify connected event to listeners.
[1;35m16:39:14.776[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$377/0x00000201af41eba0
[1;35m16:39:14.777[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m16:39:14.778[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[registerService,133][0;39m - [REGISTER-SERVICE] pedestal registering service system-x with instance Instance{instanceId='null', ip='**********', port=10004, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[1;35m16:39:14.789[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[register,76][0;39m - nacos registry, DEFAULT_GROUP system-x **********:10004 register finished
[1;35m16:39:15.069[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStarted,59][0;39m - Started SystemXApplication in 93.964 seconds (process running for 95.617)
[1;35m16:39:15.098[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_server_info' on lock
[1;35m16:39:15.098[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m16:39:15.493[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.l.i.ProductLicenseInfoServiceImpl[0;39m - [36m[printDeploymentLog,578][0;39m - LICENSE SERVER INFO  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
[1;35m16:39:15.784[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 98351分钟
[1;35m16:39:15.789[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m16:39:15.915[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:39:15.916[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:39:15.932[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(2) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m16:39:15.943[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m16:39:16.264[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[1;35m16:39:16.265[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,532][0;39m - Initializing Servlet 'dispatcherServlet'
[1;35m16:39:16.270[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,554][0;39m - Completed initialization in 5 ms
[1;35m16:39:16.411[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT
            d.`value`
        FROM
            sys_dict_data d
                LEFT JOIN sys_dict_type t ON d.type_id = t.id
        WHERE
            t.`code` = 'BUCKET'
            AND d.`status` != 2
	        AND d.`code` = ?]
Result:   [SELECT d."value" FROM sys_dict_data d LEFT JOIN sys_dict_type t ON d.type_id = t.id WHERE t."code" = 'BUCKET' AND d."status" != 2 AND d."code" = ?]
[1;35m16:39:16.501[0;39m [32m[nacos-grpc-client-executor-***********-13][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Receive server push request, request = NotifySubscriberRequest, requestId = 7183
[1;35m16:39:16.503[0;39m [32m[nacos-grpc-client-executor-***********-13][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Ack server push request, request = NotifySubscriberRequest, requestId = 7183
[1;35m16:39:18.364[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - async-utils executor is create!
[1;35m16:39:18.371[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'master:systemx_init' on lock
[1;35m16:39:18.385[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.tables where table_name = 'tenant_info' and table_schema = 'systemx';]
Result:   [SELECT count(*) FROM all_tables WHERE table_name = 'tenant_info' AND owner = 'systemx';]
[1;35m16:39:18.395[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[initNotifyWarnTimeout,72][0;39m - config listener notify warn timeout millis use default 60000 millis 
[1;35m16:39:18.395[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[<clinit>,99][0;39m - nacos.cache.data.init.snapshot = true 
[1;35m16:39:18.396[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-config.yml+DEFAULT_GROUP+pedestal
[1;35m16:39:18.397[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-config.yml, group=DEFAULT_GROUP, cnt=1
[1;35m16:39:18.397[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-config.yml, group=DEFAULT_GROUP
[1;35m16:39:18.398[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-info.yml+DEFAULT_GROUP+pedestal
[1;35m16:39:18.398[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-info.yml, group=DEFAULT_GROUP, cnt=1
[1;35m16:39:18.398[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-info.yml, group=DEFAULT_GROUP
[1;35m16:39:18.413[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] system-x.yml+DEFAULT_GROUP+pedestal
[1;35m16:39:18.413[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=system-x.yml, group=DEFAULT_GROUP, cnt=1
[1;35m16:39:18.413[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=system-x.yml, group=DEFAULT_GROUP
[1;35m16:39:18.462[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - tenant-info-service-impl executor is create!
[1;35m16:39:18.467[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:39:18.472[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:39:18.705[0;39m [32m[RMI TCP Connection(12)-198.18.0.1][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m16:39:18.728[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-3,project_systemx_1958811203346231298} inited
[1;35m16:39:18.729[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=project_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"project_systemx_1958811203346231298","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-22 16:39:18","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m16:39:18.730[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m16:39:18.732[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-4,bzmmx_systemx_1958811203421728769} inited
[1;35m16:39:18.733[0;39m [32m[Druid-ConnectionPool-Log-802539809][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=bzmmx_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"bzmmx_systemx_1958811203421728769","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-22 16:39:18","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m16:39:18.733[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m16:39:18.739[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-5,xqjii_systemx_1958811203346231299} inited
[1;35m16:39:18.740[0;39m [32m[Druid-ConnectionPool-Log-887333414][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1958811203346231299","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-22 16:39:18","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m16:39:18.741[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m16:39:19.017[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.215s)
[1;35m16:39:19.027[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.211s)
[1;35m16:39:19.027[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.215s)
[1;35m16:39:19.062[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "bzmmx_systemx": 2023.11.15.09.00.00
[1;35m16:39:19.071[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "project_systemx": 2023.11.15.09.00.00
[1;35m16:39:19.074[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "bzmmx_systemx" is up to date. No migration necessary.
[1;35m16:39:19.075[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "project_systemx" is up to date. No migration necessary.
[1;35m16:39:19.078[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "xqjii_systemx": 2023.11.15.09.00.00
[1;35m16:39:19.082[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "xqjii_systemx" is up to date. No migration necessary.
[1;35m16:39:19.083[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-4} closing ...
[1;35m16:39:19.083[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-3} closing ...
[1;35m16:39:19.083[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-4} closed
[1;35m16:39:19.083[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-3} closed
[1;35m16:39:19.084[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:39:19.090[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-5} closing ...
[1;35m16:39:19.091[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-5} closed
[1;35m16:39:19.102[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [bzmmx_systemx] success
[1;35m16:39:19.121[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [xqjii_systemx] success
[1;35m16:39:19.139[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [project_systemx] success
[1;35m16:39:19.163[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$initTenantData$3,638][0;39m - bzmmx 租户数据初始化成功
[1;35m16:39:19.164[0;39m [32m[tenant-info-service-impl3][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$initTenantData$3,638][0;39m - xqjii 租户数据初始化成功
[1;35m16:39:19.164[0;39m [32m[tenant-info-service-impl2][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$initTenantData$3,638][0;39m - project 租户数据初始化成功
[1;35m16:39:19.264[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-6,aaaaa_systemx_1958811206198358018} inited
[1;35m16:39:19.264[0;39m [32m[Druid-ConnectionPool-Log-1122545894][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=aaaaa_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"aaaaa_systemx_1958811206198358018","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-22 16:39:19","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m16:39:19.265[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m16:39:19.457[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 25 migrations (execution time 00:00.171s)
[1;35m16:39:19.493[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "aaaaa_systemx": 2023.11.15.09.00.00
[1;35m16:39:19.498[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "aaaaa_systemx" is up to date. No migration necessary.
[1;35m16:39:19.505[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-6} closing ...
[1;35m16:39:19.506[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-6} closed
[1;35m16:39:19.524[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [aaaaa_systemx] success
[1;35m16:39:19.533[0;39m [32m[tenant-info-service-impl1][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.TenantInfoServiceImpl[0;39m - [36m[lambda$initTenantData$3,638][0;39m - aaaaa 租户数据初始化成功
[1;35m16:39:19.540[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[shutdown,150][0;39m - tenant-info-service-impl executor is shutdown!
[1;35m16:39:19.543[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'master:systemx_init' is unlocked
[1;35m16:39:25.578[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Directory info : D:\x
[1;35m16:39:25.579[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk usage : 10179756400
[1;35m16:39:25.579[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk info : Drive > D:\    Total space > 70179434496    Free space > 21349412864
[1;35m16:39:25.579[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Network info : {eth5={hostname=DESKTOP-38F0Q6A, ip=**********, network-arch=Realtek Gaming GbE Family Controller, ipnet=ipv4, mac=18-C0-4D-EE-B7-42}}
[1;35m16:39:25.579[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU ID : null
[1;35m16:39:25.579[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU core : **********
[1;35m16:39:25.580[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System : Windows 11
[1;35m16:39:25.580[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System version : 10.0
[1;35m16:39:25.580[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System architecture: amd64
[1;35m16:39:25.580[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System hostname : DESKTOP-38F0Q6A
[1;35m16:39:25.580[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Total memory : 51397271552
[1;35m16:39:25.580[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - User Info : [must]
companyNameZh=优锘
mmdID=uino
validityPeriod=development
[other]
companyNameEn=uino
releaseTime=231114161431
[1;35m16:39:25.584[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_server_info' is unlocked
[1;35m16:39:45.082[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - log-manager-factory executor is create!
[1;35m16:39:45.237[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:39:45.238[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:39:45.252[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m16:39:45.252[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m16:39:45.772[0;39m [32m[nacos-grpc-client-executor-***********-26][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Receive server push request, request = NotifySubscriberRequest, requestId = 7186
[1;35m16:39:45.773[0;39m [32m[nacos-grpc-client-executor-***********-26][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [ebd7d6c6-34ed-4c71-a41d-cae0b6dcb833] Ack server push request, request = NotifySubscriberRequest, requestId = 7186
[1;35m16:40:01.425[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-7,project_systemx_1958811206022197249} inited
[1;35m16:40:01.425[0;39m [32m[Druid-ConnectionPool-Log-1823909116][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=project_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"project_systemx_1958811206022197249","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-22 16:40:01","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m16:40:01.425[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:40:02.755[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mo.a.t.u.h.p.Cookie[0;39m - [36m[log,168][0;39m - A cookie header was received [Hm_lvt_856d7b968407d0887b51e18fed5d338e=1753336151,1754377487,1754382697,1754993683;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
[1;35m16:40:02.927[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#getOpen proceed time: 161ms
[1;35m16:40:03.313[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:40:03.363[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 7ms
[1;35m16:40:03.427[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m16:40:03.882[0;39m [32m[http-nio-10004-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 11ms
[1;35m16:40:04.731[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 15ms
[1;35m16:40:05.254[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysOpLogController#opLogRecord proceed time: 75ms
[1;35m16:40:05.387[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:40:05.410[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - RealTimeMessageController#pageMessage proceed time: 65ms
[1;35m16:40:05.438[0;39m [32m[http-nio-10004-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysOpLogController#opLogRecord proceed time: 97ms
[1;35m16:40:07.600[0;39m [32m[http-nio-10004-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - RealTimeMessageController#pageMessage proceed time: 19ms
[1;35m16:40:09.628[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"activePeak":4,"activePeakTime":"2025-08-22 16:39:19","poolingCount":3,"poolingPeak":3,"poolingPeakTime":"2025-08-22 16:39:18","connectCount":26,"closeCount":26,"notEmptyWaitCount":1,"notEmptyWaitMillis":17,"physicalConnectCount":1,"executeCount":24,"pstmtCacheHitCount":10,"pstmtCacheMissCount":14,"connectionHoldTimeHistogram":[0,15,11],"clobOpenCount":452,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":3,"executeMillisMax":7,"executeMillisTotal":9,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT  id,twin_class_id,twin_class_code,acquisition_frequency,acquisition_frequency_cron,description,enable,status,create_time,create_user,update_time,update_user  FROM indicator      WHERE  (enable = ? AND status = ?)","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1},{"sql":"SELECT d.\"value\" FROM sys_dict_data d LEFT JOIN sys_dict_type t ON d.type_id = t.id WHERE t.\"code\" = 'BUCKET' AND d.\"status\" != 2 AND d.\"code\" = ?","executeCount":3,"executeMillisMax":3,"executeMillisTotal":6,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3],"clobOpenCount":3},{"sql":"SELECT count(*) FROM all_tables WHERE table_name = 'tenant_info' AND owner = 'systemx';","executeCount":1,"executeMillisMax":8,"executeMillisTotal":8,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,name,code,license_update_time,license_expired_time,license_total_time,scene_count,user_count,remark,status,create_time,create_user,update_time,update_user  FROM tenant_info      WHERE  (status <> ?)","executeCount":1,"executeMillisMax":4,"executeMillisTotal":4,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":4,"fetchRowHistogram":[0,1],"clobOpenCount":4},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":8,"executeMillisMax":20,"executeMillisTotal":56,"executeHistogram":[0,6,2],"executeAndResultHoldHistogram":[0,6,2],"concurrentMax":2,"fetchRowCount":8,"fetchRowCountMax":1,"fetchRowHistogram":[0,8]},{"sql":"SELECT  id,timer_name,action_class,cron,job_status,task_form,task_args,task_type,remark,status,create_time,create_user,update_time,update_user  FROM sys_timers      WHERE  (status <> ? AND job_status = ?)","executeCount":5,"executeMillisMax":5,"executeMillisTotal":11,"executeHistogram":[0,5],"executeAndResultHoldHistogram":[0,5],"concurrentMax":2,"fetchRowCount":20,"fetchRowCountMax":4,"fetchRowHistogram":[0,5],"clobOpenCount":40},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT  id,user_id,role_id,status,create_time,create_user,update_time,update_user  FROM sys_user_role      WHERE  (user_id = ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,role_id,menu_id,status,create_time,create_user,update_time,update_user  FROM sys_role_menu      WHERE  (role_id IN (?))","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":792,"fetchRowCountMax":792,"fetchRowHistogram":[0,0,0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND type = ? AND status = ? AND application <> ?)","executeCount":1,"executeMillisMax":4,"executeMillisTotal":4,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":405,"fetchRowCountMax":405,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":405}]}
[1;35m16:40:12.589[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-8,aaaaa_systemx_1958811207641198594} inited
[1;35m16:40:12.589[0;39m [32m[Druid-ConnectionPool-Log-1459693685][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=aaaaa_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"aaaaa_systemx_1958811207641198594","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-22 16:40:12","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m16:40:12.590[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:40:13.758[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - TenantInfoController#getMultiTenantEnable proceed time: 2ms
[1;35m16:40:13.963[0;39m [32m[http-nio-10004-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#getOpen proceed time: 210ms
[1;35m16:40:14.556[0;39m [32m[http-nio-10004-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 21ms
[1;35m16:40:14.598[0;39m [32m[http-nio-10004-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysMenuController#list proceed time: 207ms
[1;35m16:40:14.632[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m16:40:14.885[0;39m [32m[http-nio-10004-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 13ms
[1;35m16:40:14.928[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysMenuController#change proceed time: 153ms
[1;35m16:40:14.957[0;39m [32m[http-nio-10004-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 19ms
[1;35m16:40:15.067[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m16:40:15.099[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 98353分钟
[1;35m16:40:15.106[0;39m [32m[license-time-task-1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m16:40:15.447[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysOpLogController#opLogRecord proceed time: 82ms
[1;35m16:40:15.470[0;39m [32m[http-nio-10004-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - RealTimeMessageController#pageMessage proceed time: 103ms
[1;35m16:40:56.467[0;39m [32m[http-nio-10004-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysUserController#getUserById proceed time: 6ms
[1;35m16:40:56.594[0;39m [32m[http-nio-10004-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 6ms
[1;35m16:40:56.841[0;39m [32m[http-nio-10004-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysConfigController#loadValueListByCodeList proceed time: 5ms
[1;35m16:40:58.035[0;39m [32m[http-nio-10004-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - RealTimeMessageController#pageMessage proceed time: 123ms
[1;35m16:40:58.079[0;39m [32m[http-nio-10004-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - SysOpLogController#opLogRecord proceed time: 147ms
[1;35m16:41:01.427[0;39m [32m[Druid-ConnectionPool-Log-1823909116][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=project_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"project_systemx_1958811206022197249","activeCount":0,"activePeak":2,"activePeakTime":"2025-08-22 16:40:05","poolingCount":2,"poolingPeak":2,"poolingPeakTime":"2025-08-22 16:40:03","connectCount":72,"closeCount":72,"notEmptyWaitCount":1,"notEmptyWaitMillis":11,"physicalConnectCount":1,"executeCount":72,"pstmtCacheHitCount":48,"pstmtCacheMissCount":24,"connectionHoldTimeHistogram":[0,52,19,1],"clobOpenCount":503,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":8,"executeMillisTotal":8,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":36,"executeMillisMax":27,"executeMillisTotal":235,"executeHistogram":[0,30,6],"executeAndResultHoldHistogram":[0,30,6],"concurrentMax":1,"fetchRowCount":36,"fetchRowCountMax":1,"fetchRowHistogram":[0,36]},{"sql":"SELECT  id,name,code,value,sys_flag,remark,status,dice_type,group_code,sort,create_time,create_user,update_time,update_user  FROM sys_config      WHERE  (group_code = ? AND status = ?)","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":3,"fetchRowHistogram":[0,1],"clobOpenCount":3},{"sql":"SELECT  id,user_id,role_id,status,create_time,create_user,update_time,update_user  FROM sys_user_role      WHERE  (user_id = ?)","executeCount":4,"executeMillisMax":8,"executeMillisTotal":14,"executeHistogram":[0,4],"executeAndResultHoldHistogram":[0,4],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT  id,name,code,sort,data_scope_type,remark,sys_category_id,status,default_flag,create_time,create_user,update_time,update_user  FROM sys_role      WHERE  (id IN (?) AND status = ?)","executeCount":3,"executeMillisMax":2,"executeMillisTotal":5,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"UPDATE sys_user  SET account=?, password=?, password_strength=?,  nick_name=?, name=?,   sex=?,  phone=?, tel=?, last_login_ip=?, last_login_time=?, admin_type=?, status=?, user_unique_value=?,   create_time=?,  third_party_login=?,    create_user=?, update_time=?, update_user=?  WHERE id=?","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"updateCount":1,"updateCountMax":1,"updateHistogram":[0,1]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status >= ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status = ?)","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":7,"fetchRowCountMax":7,"fetchRowHistogram":[0,1],"clobOpenCount":7},{"sql":"SELECT  id,name,code,value,sys_flag,remark,status,dice_type,group_code,sort,create_time,create_user,update_time,update_user  FROM sys_config      WHERE  (code IN (?,?,?,?,?,?,?) AND status = ?)","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,0,1],"concurrentMax":1,"fetchRowCount":7,"fetchRowCountMax":7,"fetchRowHistogram":[0,1],"clobOpenCount":7},{"sql":"SELECT id,job_num,dept_id,dept_name,status,create_time,create_user,update_time,update_user FROM sys_user_detail WHERE id=?","executeCount":1,"executeMillisMax":11,"executeMillisTotal":11,"executeHistogram":[0,0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1},{"sql":"SELECT  id,role_id,menu_id,status,create_time,create_user,update_time,update_user  FROM sys_role_menu      WHERE  (role_id IN (?))","executeCount":1,"executeMillisMax":13,"executeMillisTotal":13,"executeHistogram":[0,0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":792,"fetchRowCountMax":792,"fetchRowHistogram":[0,0,0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND type = ? AND status = ? AND application <> ?)","executeCount":1,"executeMillisMax":11,"executeMillisTotal":11,"executeHistogram":[0,0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":405,"fetchRowCountMax":405,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":405},{"sql":"SELECT id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user FROM sys_user WHERE id=?","executeCount":3,"executeMillisMax":3,"executeMillisTotal":6,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT  id,name,icon,code,active,sort,status,type,plugin_id,sys_category_id,micro_app_id,visible,link,iframe_type,create_time,create_user,update_time,update_user  FROM sys_app      WHERE  (status = ? AND sort <> ? AND code <> ?) ORDER BY sort ASC","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":6,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":11,"executeMillisMax":8,"executeMillisTotal":41,"executeHistogram":[0,11],"executeAndResultHoldHistogram":[0,9,2],"concurrentMax":1,"fetchRowCount":11,"fetchRowCountMax":1,"fetchRowHistogram":[0,11]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (status = ? AND application = ? AND type NOT IN (?) AND weight NOT IN (?)) ORDER BY sort ASC","executeCount":1,"executeMillisMax":4,"executeMillisTotal":4,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1],"clobOpenCount":1},{"sql":"select value\n        from sys_config\n        where code in\n         (  \n\n            ?\n         )","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1],"clobOpenCount":1},{"sql":"SELECT  id,message,attachment_address,status,create_time,create_user,update_time,update_user  FROM sys_real_time_message      WHERE  (create_user = ?) ORDER BY create_time DESC","executeCount":2,"executeMillisMax":4,"executeMillisTotal":6,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":76,"fetchRowCountMax":38,"fetchRowHistogram":[0,0,2],"clobOpenCount":76},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":3,"fetchRowHistogram":[0,1],"clobOpenCount":3}]}
[1;35m16:41:09.630[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m16:41:12.590[0;39m [32m[Druid-ConnectionPool-Log-1459693685][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=aaaaa_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"aaaaa_systemx_1958811207641198594","activeCount":0,"activePeak":2,"activePeakTime":"2025-08-22 16:40:14","poolingCount":3,"poolingPeak":3,"poolingPeakTime":"2025-08-22 16:40:58","connectCount":112,"closeCount":112,"notEmptyWaitCount":3,"notEmptyWaitMillis":16,"physicalConnectCount":2,"executeCount":112,"pstmtCacheHitCount":80,"pstmtCacheMissCount":32,"connectionHoldTimeHistogram":[0,84,27,1],"clobOpenCount":1021,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":2,"executeMillisMax":29,"executeMillisTotal":33,"executeHistogram":[0,1,1],"executeAndResultHoldHistogram":[0,1,1],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":56,"executeMillisMax":28,"executeMillisTotal":336,"executeHistogram":[0,46,10],"executeAndResultHoldHistogram":[0,46,10],"concurrentMax":1,"fetchRowCount":56,"fetchRowCountMax":1,"fetchRowHistogram":[0,56]},{"sql":"SELECT  id,name,code,value,sys_flag,remark,status,dice_type,group_code,sort,create_time,create_user,update_time,update_user  FROM sys_config      WHERE  (group_code = ? AND status = ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":3,"fetchRowHistogram":[0,1],"clobOpenCount":3},{"sql":"SELECT  id,user_id,role_id,status,create_time,create_user,update_time,update_user  FROM sys_user_role      WHERE  (user_id = ?)","executeCount":4,"executeMillisMax":2,"executeMillisTotal":6,"executeHistogram":[1,3],"executeAndResultHoldHistogram":[0,4],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT  id,name,code,sort,data_scope_type,remark,sys_category_id,status,default_flag,create_time,create_user,update_time,update_user  FROM sys_role      WHERE  (id IN (?) AND status = ?)","executeCount":3,"executeMillisMax":1,"executeMillisTotal":4,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"UPDATE sys_user  SET account=?, password=?, password_strength=?,  nick_name=?, name=?,   sex=?,  phone=?, tel=?, last_login_ip=?, last_login_time=?, admin_type=?, status=?, user_unique_value=?,   create_time=?,  third_party_login=?,    create_user=?, update_time=?, update_user=?  WHERE id=?","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"updateCount":1,"updateCountMax":1,"updateHistogram":[0,1]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":21,"executeMillisMax":23,"executeMillisTotal":111,"executeHistogram":[0,17,4],"executeAndResultHoldHistogram":[0,16,5],"concurrentMax":2,"fetchRowCount":21,"fetchRowCountMax":1,"fetchRowHistogram":[0,21]},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status >= ?)","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status = ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":7,"fetchRowCountMax":7,"fetchRowHistogram":[0,1],"clobOpenCount":7},{"sql":"SELECT  id,name,code,value,sys_flag,remark,status,dice_type,group_code,sort,create_time,create_user,update_time,update_user  FROM sys_config      WHERE  (code IN (?,?,?,?,?,?,?) AND status = ?)","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,0,1],"concurrentMax":1,"fetchRowCount":7,"fetchRowCountMax":7,"fetchRowHistogram":[0,1],"clobOpenCount":7},{"sql":"SELECT id,job_num,dept_id,dept_name,status,create_time,create_user,update_time,update_user FROM sys_user_detail WHERE id=?","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT  id,role_id,menu_id,status,create_time,create_user,update_time,update_user  FROM sys_role_menu      WHERE  (role_id IN (?))","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":792,"fetchRowCountMax":792,"fetchRowHistogram":[0,0,0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND type = ? AND status = ? AND application <> ?)","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":405,"fetchRowCountMax":405,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":405},{"sql":"SELECT id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user FROM sys_user WHERE id=?","executeCount":4,"executeMillisMax":6,"executeMillisTotal":11,"executeHistogram":[0,4],"executeAndResultHoldHistogram":[0,4],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT  id,name,icon,code,active,sort,status,type,plugin_id,sys_category_id,micro_app_id,visible,link,iframe_type,create_time,create_user,update_time,update_user  FROM sys_app      WHERE  (status = ? AND sort <> ? AND code <> ?) ORDER BY sort ASC","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":6,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (status = ? AND application = ? AND type NOT IN (?) AND weight NOT IN (?)) ORDER BY sort ASC","executeCount":2,"executeMillisMax":2,"executeMillisTotal":4,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":25,"fetchRowCountMax":24,"fetchRowHistogram":[0,1,1],"clobOpenCount":25},{"sql":"SELECT  id,user_id,theme_json,status,create_time,create_user,update_time,update_user  FROM user_settings      WHERE  (user_id = ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT  id,pid,pids,name,code,type,icon,router,component,permission,application,open_type,visible,link,redirect,weight,sort,remark,status,default_cam_info,special,plugin_id,sys_category_id,micro_app_id,iframe_type,with_token,create_time,create_user,update_time,update_user  FROM sys_menu      WHERE  (status = ? AND application <> ?) ORDER BY sort ASC,create_time ASC","executeCount":1,"executeMillisMax":4,"executeMillisTotal":4,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":500,"fetchRowCountMax":500,"fetchRowHistogram":[0,0,0,1],"clobOpenCount":500},{"sql":"SELECT  id,name,icon,code,active,sort,status,type,plugin_id,sys_category_id,micro_app_id,visible,link,iframe_type,create_time,create_user,update_time,update_user  FROM sys_app","executeCount":1,"executeMillisMax":7,"executeMillisTotal":7,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":6,"fetchRowCountMax":6,"fetchRowHistogram":[0,1]},{"sql":"select value\n        from sys_config\n        where code in\n         (  \n\n            ?\n         )","executeCount":3,"executeMillisMax":12,"executeMillisTotal":15,"executeHistogram":[0,2,1],"executeAndResultHoldHistogram":[0,2,1],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3],"clobOpenCount":3},{"sql":"SELECT  id,name,code,icon,integration_mode,url,version,run_time,status,create_time,create_user,update_time,update_user  FROM sys_micro_app","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT  id,message,attachment_address,status,create_time,create_user,update_time,update_user  FROM sys_real_time_message      WHERE  (create_user = ?) ORDER BY create_time DESC","executeCount":2,"executeMillisMax":7,"executeMillisTotal":8,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":64,"fetchRowCountMax":32,"fetchRowHistogram":[0,0,2],"clobOpenCount":64},{"sql":"SELECT  id,name,code,sort,group_id,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_type      WHERE  (code = ? AND status <> ?)","executeCount":2,"executeMillisMax":2,"executeMillisTotal":3,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT  id,type_id,value,code,sort,remark,status,create_time,create_user,update_time,update_user  FROM sys_dict_data      WHERE  (type_id = ? AND status <> ?) ORDER BY sort ASC","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":7,"fetchRowCountMax":4,"fetchRowHistogram":[0,2],"clobOpenCount":7}]}
[1;35m16:41:13.332[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:53772
[1;35m16:41:13.333[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - tenant=Aaaaa
[1;35m16:41:15.067[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'license_online' on lock
[1;35m16:41:15.097[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.LicenseTimeTask[0;39m - [36m[timeTask,60][0;39m - 记录在线时间成功: 98355分钟
[1;35m16:41:15.102[0;39m [32m[license-time-task-2][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'license_online' is unlocked
[1;35m16:41:17.855[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,232][0;39m - Host:***********, Remote address: ***********/***********:53772
[1;35m16:41:17.856[0;39m [32m[http-nio-10004-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.s.c.AbstractWebSocketChannelHandler[0;39m - [36m[getValueFromRequest,242][0;39m - token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJBYWFhYTpkYXRhU3luYyIsInJuU3RyIjoiUXdNNUJOalBadlRyNmUxWUdKRUNpczExVHVhMHNzaXciLCJ1c2VySWQiOiIxIiwiYWNjb3VudCI6ImRhdGFTeW5jIn0.TTHuJ4GrdfSoULM5AkzhZRiO-ElBrtOZxXwCY6jNh64
