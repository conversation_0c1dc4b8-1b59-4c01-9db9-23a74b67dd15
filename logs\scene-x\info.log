[1;35m10:33:23.661[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m10:33:23.733[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m10:33:23.844[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m10:33:23.921[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m10:33:23.929[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SceneXApplication[0;39m - [36m[logStarting,53][0;39m - Starting SceneXApplication using Java 17.0.14 with PID 12704 (D:\x\pedestal-x\pedestal-x-app\scene-x\target\classes started by Administrator in D:\x)
[1;35m10:33:23.932[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SceneXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal"
[1;35m10:33:24.000[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m10:33:24.001[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m10:33:24.001[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=scene-x.yml, group=DEFAULT_GROUP] success
[1;35m10:33:26.109[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.effectpackage:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.112[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.file:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.115[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.116[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.118[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.120[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.123[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.124[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.126[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.127[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.129[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.130[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.133[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.134[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.136[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.137[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.139[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.140[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.141[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.142[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.143[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.144[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.145[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.148[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.148[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.149[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.149[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.150[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.151[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.151[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.152[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.153[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.154[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.155[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.156[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.157[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.159[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.160[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.165[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.bubble:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.166[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.model:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.170[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.tenant:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.173[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.projection:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.174[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.projection:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.176[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.180[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.182[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.184[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.187[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.190[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.192[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.199[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.202[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.204[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.206[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.211[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.config:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m10:33:26.522[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'paginationInnerInterceptor' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=mybatisConfig; factoryMethodName=paginationInnerInterceptor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/app/scenex/config/MybatisConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.uino.x.common.dameng.CommonXDmConfig; factoryMethodName=paginationInnerInterceptor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/common/dameng/CommonXDmConfig.class]]
[1;35m10:33:27.057[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m10:33:27.065[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[1;35m10:33:27.114[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
[1;35m10:33:27.124[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m10:33:27.127[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[1;35m10:33:27.143[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
[1;35m10:33:27.157[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m10:33:27.159[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m10:33:27.182[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[1;35m10:33:27.768[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=ba840269-22a8-36b7-99b6-870d0e77a363
[1;35m10:33:27.848[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.s.SqlTransformationStrategyFactory[0;39m - [36m[createReplaceIntoStrategy,215][0;39m - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
[1;35m10:33:27.854[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.s.SqlTransformationStrategyFactory[0;39m - [36m[createDefaultStrategies,103][0;39m - Created 9 default transformation strategies
[1;35m10:33:27.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[logStrategyRegistration,625][0;39m - Registered 9 transformation strategies in engine [dm]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, BacktickQuoteTransformationStrategy]
[1;35m10:33:27.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[<init>,235][0;39m - SQL transformation engine [dm] initialized with 9 strategies
[1;35m10:33:27.914[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-1} inited
[1;35m10:33:28.032[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,346][0;39m - SQL transformation statistics: 
SqlTransformationEngine [dm] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 9 registered
  Memory: Estimated Size=0 bytes
[1;35m10:33:28.106[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:33:28.141[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-1} closing ...
[1;35m10:33:28.143[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-1} closed
[1;35m10:33:29.845[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 10007 (http)
[1;35m10:33:29.887[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-10007"]
[1;35m10:33:29.889[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m10:33:29.890[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.43]
[1;35m10:33:30.009[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m10:33:30.010[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 6007 ms
[1;35m10:33:32.026[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:32.230[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [scenex] success
[1;35m10:33:32.231[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[afterPropertiesSet,241][0;39m - dynamic-datasource initial loaded [1] datasource,primary datasource named [scenex]
[1;35m10:33:32.349[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-2,scenex} inited
[1;35m10:33:32.352[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:33:32","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:33:34.712[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m10:33:35.482[0;39m [32m[redisson-netty-1-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m10:33:35.510[0;39m [32m[redisson-netty-1-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m10:33:35.847[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:33:35.978[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 18 migrations (execution time 00:00.101s)
[1;35m10:33:36.015[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "scenex": 2025.07.22.18.56.56
[1;35m10:33:36.021[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "scenex" is up to date. No migration necessary.
[1;35m10:33:36.693[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:36.766[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - things-models-service-impl executor is create!
[1;35m10:33:36.856[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:36.875[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - data-migration-service-impl executor is create!
[1;35m10:33:36.890[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - models-service-impl executor is create!
[1;35m10:33:37.005[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:37.177[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:37.340[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m10:33:37.376[0;39m [32m[redisson-netty-5-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m10:33:37.401[0;39m [32m[redisson-netty-5-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m10:33:37.446[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:37.477[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:37.507[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:37.572[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:37.592[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:37.682[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - data-migration-service-impl executor is create!
[1;35m10:33:38.174[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:38.279[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:38.293[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:38.759[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:39.532[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:40.175[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:40.507[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:40.605[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:40.701[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:40.722[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:40.971[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - twin-body-data-service-impl executor is create!
[1;35m10:33:41.053[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'scene-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:33:41.765[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m10:33:41.828[0;39m [32m[redisson-netty-8-7][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m10:33:41.854[0;39m [32m[redisson-netty-8-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m10:33:42.128[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.SentinelWebMvcConfigurer[0;39m - [36m[addInterceptors,52][0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[1;35m10:33:42.874[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.m.e.s.MybatisPlusApplicationContextAware[0;39m - [36m[setApplicationContext,40][0;39m - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1e1232cf
[1;35m10:33:44.320[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 1 endpoint beneath base path '/actuator'
[1;35m10:33:45.294[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Starting ProtocolHandler ["http-nio-10007"]
[1;35m10:33:45.307[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[start,243][0;39m - Tomcat started on port 10007 (http) with context path '/'
[1;35m10:33:45.312[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[init,102][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m10:33:45.314[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[initNamespaceForNaming,62][0;39m - initializer namespace from ans.namespace attribute : null
[1;35m10:33:45.314[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$0,66][0;39m - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[1;35m10:33:45.314[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$1,73][0;39m - initializer namespace from namespace attribute :null
[1;35m10:33:45.321[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,75][0;39m - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[1;35m10:33:45.325[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m10:33:45.325[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m10:33:45.447[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of e4b18e31-0b5a-4d28-ad9c-4539100e2819
[1;35m10:33:45.449[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,109][0;39m - Create naming rpc client for uuid->e4b18e31-0b5a-4d28-ad9c-4539100e2819
[1;35m10:33:45.449[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[1;35m10:33:45.450[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[1;35m10:33:45.450[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[1;35m10:33:45.451[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m10:33:45.451[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:33:45.487[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Success to connect to server [***********:8848] on start up, connectionId = 1756089225293_**********_53546
[1;35m10:33:45.488[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m10:33:45.488[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify connected event to listeners.
[1;35m10:33:45.488[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$378/0x00000203e54223d0
[1;35m10:33:45.488[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m10:33:45.490[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[registerService,133][0;39m - [REGISTER-SERVICE] pedestal registering service scene-x with instance Instance{instanceId='null', ip='**********', port=10007, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[1;35m10:33:45.505[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[register,76][0;39m - nacos registry, DEFAULT_GROUP scene-x **********:10007 register finished
[1;35m10:33:45.705[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SceneXApplication[0;39m - [36m[logStarted,59][0;39m - Started SceneXApplication in 25.622 seconds (process running for 26.841)
[1;35m10:33:45.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - async-utils executor is create!
[1;35m10:33:45.734[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,104][0;39m - 'master:scenex_init' on lock
[1;35m10:33:45.853[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[initNotifyWarnTimeout,72][0;39m - config listener notify warn timeout millis use default 60000 millis 
[1;35m10:33:45.854[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[<clinit>,99][0;39m - nacos.cache.data.init.snapshot = true 
[1;35m10:33:45.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] scene-x.yml+DEFAULT_GROUP+pedestal
[1;35m10:33:45.875[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=scene-x.yml, group=DEFAULT_GROUP, cnt=1
[1;35m10:33:45.876[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=scene-x.yml, group=DEFAULT_GROUP
[1;35m10:33:45.890[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-config.yml+DEFAULT_GROUP+pedestal
[1;35m10:33:45.890[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-config.yml, group=DEFAULT_GROUP, cnt=1
[1;35m10:33:45.890[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-config.yml, group=DEFAULT_GROUP
[1;35m10:33:45.891[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-info.yml+DEFAULT_GROUP+pedestal
[1;35m10:33:45.892[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-info.yml, group=DEFAULT_GROUP, cnt=1
[1;35m10:33:45.892[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-info.yml, group=DEFAULT_GROUP
[1;35m10:33:45.903[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [systemx] success
[1;35m10:33:45.930[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-3,systemx_1959806374981775362} inited
[1;35m10:33:45.930[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:33:45","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:33:45.935[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.tables where table_name = 'tenant_info' and table_schema = 'systemx';]
Result:   [SELECT count(*) FROM all_tables WHERE table_name = 'tenant_info' AND owner = 'systemx';]
[1;35m10:33:45.984[0;39m [32m[RMI TCP Connection(3)-198.18.0.1][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[1;35m10:33:45.985[0;39m [32m[RMI TCP Connection(3)-198.18.0.1][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,532][0;39m - Initializing Servlet 'dispatcherServlet'
[1;35m10:33:45.995[0;39m [32m[RMI TCP Connection(3)-198.18.0.1][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,554][0;39m - Completed initialization in 10 ms
[1;35m10:33:46.015[0;39m [32m[RMI TCP Connection(5)-198.18.0.1][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m10:33:46.031[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - init-config-runner executor is create!
[1;35m10:33:46.036[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:33:46.038[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:33:46.126[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-6,project_scenex_1959806375627698178} inited
[1;35m10:33:46.127[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-4,xqjii_scenex_1959806375627698177} inited
[1;35m10:33:46.127[0;39m [32m[Druid-ConnectionPool-Log-620624658][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=project_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"project_scenex_1959806375627698178","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:33:46","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:33:46.127[0;39m [32m[Druid-ConnectionPool-Log-89677387][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806375627698177","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:33:46","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:33:46.127[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:33:46.127[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:33:46.136[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-5,bzmmx_scenex_1959806375657058306} inited
[1;35m10:33:46.136[0;39m [32m[Druid-ConnectionPool-Log-111835600][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=bzmmx_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"bzmmx_scenex_1959806375657058306","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:33:46","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":4,"executeMillisTotal":4,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:33:46.137[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:33:46.476[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 18 migrations (execution time 00:00.287s)
[1;35m10:33:46.477[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 18 migrations (execution time 00:00.302s)
[1;35m10:33:46.493[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 18 migrations (execution time 00:00.318s)
[1;35m10:33:46.520[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "bzmmx_scenex": 2025.07.22.18.56.56
[1;35m10:33:46.523[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "bzmmx_scenex" is up to date. No migration necessary.
[1;35m10:33:46.528[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.InitConfigRunner[0;39m - [36m[lambda$2,79][0;39m - bzmmx 租户数据初始化成功
[1;35m10:33:46.529[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-5} closing ...
[1;35m10:33:46.529[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-5} closed
[1;35m10:33:46.530[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:33:46.545[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [bzmmx_scenex] success
[1;35m10:33:46.587[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-7,aaaaa_scenex_1959806377724850178} inited
[1;35m10:33:46.587[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=aaaaa_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"aaaaa_scenex_1959806377724850178","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:33:46","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:33:46.588[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: jdbc:dm://***********:5236 (DM DBMS 8.1)
[1;35m10:33:46.596[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "project_scenex": 2025.07.22.18.56.56
[1;35m10:33:46.612[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "project_scenex" is up to date. No migration necessary.
[1;35m10:33:46.624[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.InitConfigRunner[0;39m - [36m[lambda$2,79][0;39m - project 租户数据初始化成功
[1;35m10:33:46.624[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-6} closing ...
[1;35m10:33:46.625[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-6} closed
[1;35m10:33:46.625[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "xqjii_scenex": 2025.07.22.18.56.56
[1;35m10:33:46.631[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "xqjii_scenex" is up to date. No migration necessary.
[1;35m10:33:46.635[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.InitConfigRunner[0;39m - [36m[lambda$2,79][0;39m - xqjii 租户数据初始化成功
[1;35m10:33:46.636[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-4} closing ...
[1;35m10:33:46.636[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-4} closed
[1;35m10:33:46.639[0;39m [32m[init-config-runner2][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [project_scenex] success
[1;35m10:33:46.650[0;39m [32m[init-config-runner3][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [xqjii_scenex] success
[1;35m10:33:46.667[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 18 migrations (execution time 00:00.071s)
[1;35m10:33:46.701[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema "aaaaa_scenex": 2025.07.22.18.56.56
[1;35m10:33:46.710[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema "aaaaa_scenex" is up to date. No migration necessary.
[1;35m10:33:46.714[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.c.InitConfigRunner[0;39m - [36m[lambda$2,79][0;39m - aaaaa 租户数据初始化成功
[1;35m10:33:46.715[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-7} closing ...
[1;35m10:33:46.715[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-7} closed
[1;35m10:33:46.728[0;39m [32m[init-config-runner1][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [aaaaa_scenex] success
[1;35m10:33:46.728[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[shutdown,150][0;39m - init-config-runner executor is shutdown!
[1;35m10:33:46.732[0;39m [32m[async-utils1][0;39m [34mINFO [0;39m [36mc.u.x.c.r.r.RedissonLockAdvice[0;39m - [36m[redissonLock,149][0;39m - 'master:scenex_init' is unlocked
[1;35m10:34:09.361[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mo.a.t.u.h.p.Cookie[0;39m - [36m[log,168][0;39m - A cookie header was received [Hm_lvt_856d7b968407d0887b51e18fed5d338e=**********,**********,**********,**********;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
[1;35m10:34:09.644[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:34:09.654[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:34:09.667[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:34:09.766[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m10:34:09.767[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m10:34:09.800[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(2) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:34:09.802[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:34:09.984[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-8,xqjii_scenex_1959806378127503362} inited
[1;35m10:34:09.984[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:34:09","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:34:09.984[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:34:10.100[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:34:15.613[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7207
[1;35m10:34:16.921[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7207
[1;35m10:34:17.165[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7208
[1;35m10:34:19.728[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7208
[1;35m10:34:39.323[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"activePeak":3,"activePeakTime":"2025-08-25 10:33:36","poolingCount":3,"poolingPeak":3,"poolingPeakTime":"2025-08-25 10:33:46","connectCount":14,"closeCount":13,"notEmptyWaitCount":4,"notEmptyWaitMillis":40,"physicalConnectCount":3,"executeCount":21,"commitCount":5,"pstmtCacheHitCount":9,"pstmtCacheMissCount":12,"startTransactionCount":5,"transactionHistogram":[0,3,2],"connectionHoldTimeHistogram":[0,7,5,1],"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":3,"executeMillisMax":3,"executeMillisTotal":5,"executeHistogram":[2,1],"executeAndResultHoldHistogram":[2,1],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') FROM DUAL","executeCount":4,"executeMillisMax":0,"executeMillisTotal":3,"executeHistogram":[4],"executeAndResultHoldHistogram":[3,1],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT USER FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT CASE WHEN EXISTS(SELECT DISTINCT OBJECT_NAME FROM ALL_OBJECTS WHERE OBJECT_TYPE = 'SCH' AND OBJECT_NAME = ?) THEN 1 ELSE 0 END FROM DUAL","executeCount":2,"executeMillisMax":1,"executeMillisTotal":3,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2],"inTransactionCount":1},{"sql":"SELECT \"installed_rank\",\"version\",\"description\",\"type\",\"script\",\"checksum\",\"installed_on\",\"installed_by\",\"execution_time\",\"success\" FROM \"scenex\".\"flyway_schema_history\" WHERE \"installed_rank\" > ? ORDER BY \"installed_rank\"","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[1,1],"concurrentMax":1,"fetchRowCount":18,"fetchRowCountMax":18,"fetchRowHistogram":[1,0,1],"inTransactionCount":2},{"sql":"LOCK TABLE \"scenex\".\"flyway_schema_history\" IN EXCLUSIVE MODE","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"inTransactionCount":1},{"sql":"ALTER SESSION SET CURRENT_SCHEMA=\"scenex\"","executeCount":2,"executeMillisMax":0,"executeMillisTotal":1,"executeHistogram":[2],"executeAndResultHoldHistogram":[2],"concurrentMax":1,"inTransactionCount":2},{"sql":"SELECT  id,twin_class_id,twin_class_code,query_url,query_http_method,query_request_body,query_request_header,query_result,task_name,update_method,execution_cycle,execution_cron,enable,status,create_time,create_user,update_time,update_user  FROM twin_property      WHERE  (enable = ? AND execution_cron IS NOT NULL AND status = ?)","executeCount":1,"executeMillisMax":8,"executeMillisTotal":8,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":8,"executeMillisMax":6,"executeMillisTotal":30,"executeHistogram":[0,8],"executeAndResultHoldHistogram":[0,8],"concurrentMax":2,"fetchRowCount":8,"fetchRowCountMax":1,"fetchRowHistogram":[0,8]}]}
[1;35m10:34:39.329[0;39m [32m[nacos-grpc-client-executor-***********-40][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 7209
[1;35m10:34:39.329[0;39m [32m[nacos-grpc-client-executor-***********-40][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 7209
[1;35m10:34:39.534[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - sql-utils executor is create!
[1;35m10:34:39.537[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:34:39.546[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:34:39.550[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:34:39.553[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:34:39.557[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:34:39.561[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:34:39.566[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:34:39.571[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:34:39.575[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:34:39.579[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:34:39.583[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:34:39.586[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:34:39.590[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:34:39.595[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:34:39.598[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:34:39.602[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:34:39.605[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:34:39.610[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:34:39.614[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:34:39.618[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:34:39.622[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:34:39.626[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:34:39.774[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:34:39.777[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - log-manager-factory executor is create!
[1;35m10:34:39.856[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:34:39.879[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [xqjii_systemx] success
[1;35m10:34:39.902[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-9,xqjii_systemx_1959806601352556545} inited
[1;35m10:34:39.902[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:34:39","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:34:40.012[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'log-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m10:34:40.064[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m10:34:40.064[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m10:34:40.314[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:34:40.314[0;39m [32m[log-manager-factory1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:34:40.906[0;39m [32m[nacos-grpc-client-executor-***********-31][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7210
[1;35m10:34:40.907[0;39m [32m[nacos-grpc-client-executor-***********-31][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7210
[1;35m10:34:45.933[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:33:45","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:33:45","connectCount":2,"closeCount":2,"executeCount":2,"pstmtCacheMissCount":2,"connectionHoldTimeHistogram":[0,0,2],"clobOpenCount":4,"sqlList":[{"sql":"SELECT count(*) FROM all_tables WHERE table_name = 'tenant_info' AND owner = 'systemx';","executeCount":1,"executeMillisMax":8,"executeMillisTotal":8,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,name,code,license_update_time,license_expired_time,license_total_time,scene_count,user_count,remark,status,create_time,create_user,update_time,update_user  FROM tenant_info      WHERE  (status <> ?)","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":4,"fetchRowHistogram":[0,1],"clobOpenCount":4}]}
[1;35m10:35:09.995[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":1,"activePeak":2,"activePeakTime":"2025-08-25 10:34:39","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:34:09","connectCount":9,"closeCount":8,"notEmptyWaitCount":1,"notEmptyWaitMillis":17,"physicalConnectCount":1,"executeCount":8,"errorCount":1,"pstmtCacheHitCount":2,"pstmtCacheMissCount":5,"connectionHoldTimeHistogram":[0,6,1,1],"clobOpenCount":2,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":4,"executeMillisMax":6,"executeMillisTotal":20,"executeHistogram":[0,4],"executeAndResultHoldHistogram":[0,4],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]},{"sql":"SELECT id,name,code,group_id,level,data_type,custom_color,bubble_info_id,things_model_id,things_model_uuid,status,form,structure,pure,create_time,create_user,update_time,update_user FROM twin_class WHERE id=?","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1],"clobOpenCount":2},{"sql":"SELECT  id,twin_coalesce_id,twin_code,twin_coalesce_model,twin_model,status,create_time,create_user,update_time,update_user  FROM twin_coalesce_map      WHERE  (twin_code = ? AND twin_model IS NOT NULL AND status = ?)","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT id, twin_class_id, twin_class_code, field_name, \"condition\", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"drop table if exists \"xqjii_scenex\".\"tmp_twin_zhu\"\n;\nCREATE TABLE \"xqjii_scenex\".\"tmp_twin_zhu\" (\n  \"uuid\" BIGINT NOT NULL,\n  \"scene_id\" VARCHAR(127),\n  \"wgs84_position\" CLOB,\n  \"gcj02_position\" CLOB,\n  \"gis_height\" VARCHAR(255),\n  \"position_body\" CLOB,\n  \"data_source\" VARCHAR(255),\n  \"parent_user_id\" VARCHAR(127),\n  \"user_room_id\" VARCHAR(127),\n  \"position\" VARCHAR(255),\n  \"current_level\" VARCHAR(127),\n  \"user_floor_id\" VARCHAR(127),\n  \"user_building_id\" VARCHAR(127),\n  \"UniqueCode\" VARCHAR(255),\n  \"create_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"create_user\" VARCHAR(127),\n  \"update_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"update_user\" VARCHAR(127),\n  \"LAIZHEHEREN\" VARCHAR(255)\n,  PRIMARY KEY (\"uuid\")\n)\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"uuid\" IS '唯一标识'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"scene_id\" IS '所属场景'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"wgs84_position\" IS 'WGS84坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gcj02_position\" IS 'GCJ02火星坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gis_height\" IS '离地高度'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position_body\" IS '点位数据信息'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"data_source\" IS '点位数据来源'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"parent_user_id\" IS '场景层级中的物体id'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_room_id\" IS '所属房间ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position\" IS '位置坐标'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"current_level\" IS '所在层级'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_floor_id\" IS '所属楼层ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_building_id\" IS '所属建筑ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"UniqueCode\" IS '孪生体编码'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_time\" IS '创建时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_user\" IS '创建人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_time\" IS '更新时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_user\" IS '更新人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"LAIZHEHEREN\" IS '来者何人'\n;\nCREATE UNIQUE INDEX \"LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37\" ON \"xqjii_scenex\".\"tmp_twin_zhu\" (\"LAIZHEHEREN\")","executeCount":1,"executeMillisMax":137,"executeMillisTotal":137,"executeHistogram":[0,0,0,1],"executeAndResultHoldHistogram":[0,0,0,1],"executeErrorCount":1,"concurrentMax":1}]}
[1;35m10:35:39.324[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:35:39.903[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:34:39","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:34:39","connectCount":1,"closeCount":1,"executeCount":1,"pstmtCacheMissCount":1,"connectionHoldTimeHistogram":[0,1],"sqlList":[{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]}]}
[1;35m10:35:45.935[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:36:09.997[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":1,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:36:39.339[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:36:39.910[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:36:45.936[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:37:09.998[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":1,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:37:39.339[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:37:39.911[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:37:45.937[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:37:56.772[0;39m [32m[nacos-grpc-client-executor-***********-104][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7215
[1;35m10:37:56.774[0;39m [32m[nacos-grpc-client-executor-***********-104][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,107][0;39m - removed ips(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:37:56.775[0;39m [32m[nacos-grpc-client-executor-***********-104][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:37:56.782[0;39m [32m[nacos-grpc-client-executor-***********-104][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7215
[1;35m10:37:58.277[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:00.461[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:39:00.462[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":2,"activePeak":2,"activePeakTime":"2025-08-25 10:37:58","poolingCount":0,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:37:58","connectCount":7,"closeCount":6,"executeCount":6,"pstmtCacheHitCount":3,"pstmtCacheMissCount":3,"connectionHoldTimeHistogram":[0,6],"clobOpenCount":2,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":3,"executeMillisMax":6,"executeMillisTotal":11,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT id,name,code,group_id,level,data_type,custom_color,bubble_info_id,things_model_id,things_model_uuid,status,form,structure,pure,create_time,create_user,update_time,update_user FROM twin_class WHERE id=?","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1],"clobOpenCount":2},{"sql":"SELECT  id,twin_coalesce_id,twin_code,twin_coalesce_model,twin_model,status,create_time,create_user,update_time,update_user  FROM twin_coalesce_map      WHERE  (twin_code = ? AND twin_model IS NOT NULL AND status = ?)","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT id, twin_class_id, twin_class_code, field_name, \"condition\", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1}]}
[1;35m10:39:00.463[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:39:00.464[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:39:00.469[0;39m [32m[nacos-grpc-client-executor-***********-105][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = ClientDetectionRequest, requestId = 7219
[1;35m10:39:00.469[0;39m [32m[nacos-grpc-client-executor-***********-121][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 7218
[1;35m10:39:00.470[0;39m [32m[nacos-grpc-client-executor-***********-105][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = ClientDetectionRequest, requestId = 7219
[1;35m10:39:00.470[0;39m [32m[nacos-grpc-client-executor-***********-121][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 7218
[1;35m10:39:02.559[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:02.560[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:02.561[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:02.561[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:02.562[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:02.562[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:02.563[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:02.563[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:02.564[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:02.564[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:02.565[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:02.565[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:02.565[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:02.566[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:02.567[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:02.567[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:02.568[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:02.569[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:02.569[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:02.569[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:02.569[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:02.570[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:02.577[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Server healthy check fail, currentConnection = 1756089202353_**********_53370
[1;35m10:39:02.577[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:39:02.577[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:39:02.578[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Server healthy check fail, currentConnection = 1756089225293_**********_53546
[1;35m10:39:02.578[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:39:02.578[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:39:02.599[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Success to connect a server [***********:8848], connectionId = 1756089542417_**********_54944
[1;35m10:39:02.599[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Abandon prev connection, server is ***********:8848, connectionId is 1756089225293_**********_53546
[1;35m10:39:02.599[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089225293_**********_53546
[1;35m10:39:02.599[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Success to connect a server [***********:8848], connectionId = 1756089542416_**********_54942
[1;35m10:39:02.600[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Abandon prev connection, server is ***********:8848, connectionId is 1756089202353_**********_53370
[1;35m10:39:02.600[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089202353_**********_53370
[1;35m10:39:02.603[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:39:02.603[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify disconnected event to listeners
[1;35m10:39:02.603[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:39:02.603[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify disconnected event to listeners
[1;35m10:39:02.605[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:39:02.605[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:39:02.605[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onDisConnect,720][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] DisConnected,clear listen context...
[1;35m10:39:02.605[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify connected event to listeners.
[1;35m10:39:02.605[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Connected,notify listen context...
[1;35m10:39:02.605[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify connected event to listeners.
[1;35m10:39:02.606[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m10:39:02.622[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Success to connect a server [***********:8848], connectionId = 1756089542441_**********_54946
[1;35m10:39:02.623[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Abandon prev connection, server is ***********:8848, connectionId is 1756089542416_**********_54942
[1;35m10:39:02.623[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089542416_**********_54942
[1;35m10:39:02.624[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify disconnected event to listeners
[1;35m10:39:02.624[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onDisConnect,720][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] DisConnected,clear listen context...
[1;35m10:39:02.624[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify connected event to listeners.
[1;35m10:39:02.624[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Connected,notify listen context...
[1;35m10:39:02.632[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Success to connect a server [***********:8848], connectionId = 1756089542445_**********_54948
[1;35m10:39:02.633[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Abandon prev connection, server is ***********:8848, connectionId is 1756089542417_**********_54944
[1;35m10:39:02.633[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089542417_**********_54944
[1;35m10:39:02.633[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify disconnected event to listeners
[1;35m10:39:02.633[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify connected event to listeners.
[1;35m10:39:02.633[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m10:39:02.652[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:02.653[0;39m [32m[log-manager-factory2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:02.670[0;39m [32m[log-manager-factory2][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m10:39:02.671[0;39m [32m[log-manager-factory2][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m10:39:03.208[0;39m [32m[nacos-grpc-client-executor-***********-128][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7222
[1;35m10:39:03.208[0;39m [32m[nacos-grpc-client-executor-***********-128][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7222
[1;35m10:39:03.475[0;39m [32m[com.alibaba.nacos.client.naming.grpc.redo.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[redoForInstance,73][0;39m - Redo instance operation REGISTER for DEFAULT_GROUP@@scene-x
[1;35m10:39:03.485[0;39m [32m[com.alibaba.nacos.client.naming.grpc.redo.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[redoForSubscribe,121][0;39m - Redo subscriber operation REGISTER for DEFAULT_GROUP@@system-x#
[1;35m10:39:04.071[0;39m [32m[nacos-grpc-client-executor-***********-133][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7223
[1;35m10:39:04.072[0;39m [32m[nacos-grpc-client-executor-***********-133][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7223
[1;35m10:39:25.273[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:25.481[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:25.481[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:25.482[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:25.482[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:25.482[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:25.482[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:25.482[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:25.483[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:25.483[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:25.483[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:25.483[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:25.484[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:25.484[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:25.484[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:25.484[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:25.484[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:25.484[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:25.484[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:25.485[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:25.485[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:25.485[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:25.485[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:25.552[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:25.554[0;39m [32m[log-manager-factory3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:28.640[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:28.811[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:28.812[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:28.812[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:28.812[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:28.812[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:28.814[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:28.814[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:28.814[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:28.814[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:28.814[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:28.814[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:28.815[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:28.815[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:28.815[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:28.815[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:28.815[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:28.815[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:28.816[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:28.816[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:28.816[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:28.816[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:28.816[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:29.635[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:29.638[0;39m [32m[log-manager-factory4][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:35.805[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:35.997[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:35.998[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:35.998[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:35.998[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:35.998[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:35.998[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:35.999[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:35.999[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:35.999[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:35.999[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:35.999[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:35.999[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:35.999[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:36.000[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:36.000[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:36.000[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:36.000[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:36.000[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:36.000[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:36.000[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:36.001[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:36.001[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:36.061[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:36.063[0;39m [32m[log-manager-factory5][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:37.953[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:38.218[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:38.219[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:38.219[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:38.220[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:38.220[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:38.220[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:38.221[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:38.221[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:38.221[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:38.221[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:38.221[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:38.221[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:38.221[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:38.222[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:38.222[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:38.222[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:38.222[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:38.222[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:38.222[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:38.222[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:38.223[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:38.223[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:38.289[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:38.290[0;39m [32m[log-manager-factory3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:41.454[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:41.703[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:41.703[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:41.703[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:41.704[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:41.704[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:41.704[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:41.705[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:41.705[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:41.705[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:41.705[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:41.706[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:41.706[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:41.706[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:41.706[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:41.706[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:41.706[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:41.708[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:41.708[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:41.708[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:41.708[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:41.708[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:41.709[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:41.793[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:41.795[0;39m [32m[log-manager-factory4][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:45.473[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - TwinClassGroupController#tree proceed time: 84ms
[1;35m10:39:45.912[0;39m [32m[http-nio-10007-exec-9][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - ThingsModelController#getOneByModelId proceed time: 13ms
[1;35m10:39:45.984[0;39m [32m[http-nio-10007-exec-10][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - ThingsModelController#getOneByModelId proceed time: 6ms
[1;35m10:39:54.784[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:55.030[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:55.031[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:55.031[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:55.031[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:55.031[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:55.031[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:55.031[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:55.032[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:55.032[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:55.032[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:55.033[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:55.033[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:55.033[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:55.033[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:55.033[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:55.033[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:55.033[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:55.034[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:55.034[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:55.034[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:55.034[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:55.034[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:55.209[0;39m [32m[http-nio-10007-exec-1][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:55.210[0;39m [32m[log-manager-factory5][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:39:57.244[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:39:57.399[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:39:57.399[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:39:57.399[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:39:57.400[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:39:57.400[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:39:57.400[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:39:57.401[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:39:57.402[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:39:57.402[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:39:57.402[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:39:57.402[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:39:57.402[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:39:57.403[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:39:57.404[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:39:57.404[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:39:57.517[0;39m [32m[http-nio-10007-exec-2][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,205][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:39:57.519[0;39m [32m[log-manager-factory3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:40:00.464[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:40:00.465[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:40:00.470[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:39:02","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:39:02","connectCount":11,"closeCount":11,"executeCount":11,"pstmtCacheHitCount":11,"connectionHoldTimeHistogram":[0,9,2],"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":11,"executeMillisMax":13,"executeMillisTotal":42,"executeHistogram":[0,10,1],"executeAndResultHoldHistogram":[0,10,1],"concurrentMax":1,"fetchRowCount":11,"fetchRowCountMax":1,"fetchRowHistogram":[0,11]}]}
[1;35m10:40:00.470[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":9,"activePeak":10,"activePeakTime":"2025-08-25 10:39:57","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:39:00","connectCount":76,"closeCount":69,"notEmptyWaitCount":8,"notEmptyWaitMillis":132,"physicalConnectCount":8,"executeCount":69,"errorCount":8,"pstmtCacheHitCount":29,"pstmtCacheMissCount":32,"connectionHoldTimeHistogram":[0,50,11,7,1],"clobOpenCount":14,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":8,"executeMillisMax":4,"executeMillisTotal":13,"executeHistogram":[2,6],"executeAndResultHoldHistogram":[2,6],"concurrentMax":1,"fetchRowCount":8,"fetchRowCountMax":1,"fetchRowHistogram":[0,8]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":36,"executeMillisMax":15,"executeMillisTotal":162,"executeHistogram":[0,33,3],"executeAndResultHoldHistogram":[0,33,3],"concurrentMax":1,"fetchRowCount":36,"fetchRowCountMax":1,"fetchRowHistogram":[0,36]},{"sql":"SELECT id,name,code,group_id,level,data_type,custom_color,bubble_info_id,things_model_id,things_model_uuid,status,form,structure,pure,create_time,create_user,update_time,update_user FROM twin_class WHERE id=?","executeCount":7,"executeMillisMax":3,"executeMillisTotal":8,"executeHistogram":[5,2],"executeAndResultHoldHistogram":[0,6,1],"concurrentMax":1,"fetchRowCount":7,"fetchRowCountMax":1,"fetchRowHistogram":[0,7],"clobOpenCount":14},{"sql":"SELECT  id,twin_coalesce_id,twin_code,twin_coalesce_model,twin_model,status,create_time,create_user,update_time,update_user  FROM twin_coalesce_map      WHERE  (twin_code = ? AND twin_model IS NOT NULL AND status = ?)","executeCount":7,"executeMillisMax":1,"executeMillisTotal":6,"executeHistogram":[5,2],"executeAndResultHoldHistogram":[1,6],"concurrentMax":1},{"sql":"SELECT id, twin_class_id, twin_class_code, field_name, \"condition\", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC","executeCount":7,"executeMillisMax":1,"executeMillisTotal":6,"executeHistogram":[5,2],"executeAndResultHoldHistogram":[0,7],"concurrentMax":1},{"sql":"drop table if exists \"xqjii_scenex\".\"tmp_twin_zhu\"\n;\nCREATE TABLE \"xqjii_scenex\".\"tmp_twin_zhu\" (\n  \"uuid\" BIGINT NOT NULL,\n  \"scene_id\" VARCHAR(127),\n  \"wgs84_position\" CLOB,\n  \"gcj02_position\" CLOB,\n  \"gis_height\" VARCHAR(255),\n  \"position_body\" CLOB,\n  \"data_source\" VARCHAR(255),\n  \"parent_user_id\" VARCHAR(127),\n  \"user_room_id\" VARCHAR(127),\n  \"position\" VARCHAR(255),\n  \"current_level\" VARCHAR(127),\n  \"user_floor_id\" VARCHAR(127),\n  \"user_building_id\" VARCHAR(127),\n  \"UniqueCode\" VARCHAR(255),\n  \"create_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"create_user\" VARCHAR(127),\n  \"update_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"update_user\" VARCHAR(127),\n  \"LAIZHEHEREN\" VARCHAR(255)\n,  PRIMARY KEY (\"uuid\")\n)\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"uuid\" IS '唯一标识'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"scene_id\" IS '所属场景'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"wgs84_position\" IS 'WGS84坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gcj02_position\" IS 'GCJ02火星坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gis_height\" IS '离地高度'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position_body\" IS '点位数据信息'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"data_source\" IS '点位数据来源'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"parent_user_id\" IS '场景层级中的物体id'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_room_id\" IS '所属房间ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position\" IS '位置坐标'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"current_level\" IS '所在层级'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_floor_id\" IS '所属楼层ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_building_id\" IS '所属建筑ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"UniqueCode\" IS '孪生体编码'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_time\" IS '创建时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_user\" IS '创建人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_time\" IS '更新时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_user\" IS '更新人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"LAIZHEHEREN\" IS '来者何人'\n;\nCREATE UNIQUE INDEX \"LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37\" ON \"xqjii_scenex\".\"tmp_twin_zhu\" (\"LAIZHEHEREN\")","executeCount":8,"executeMillisMax":816,"executeMillisTotal":1459,"executeHistogram":[0,0,5,3],"executeAndResultHoldHistogram":[0,0,5,3],"executeErrorCount":8,"concurrentMax":1},{"sql":"SELECT  id,name,pid,pids,status,sort,create_time,create_user,update_time,update_user  FROM twin_class_group      WHERE  (status <> ?) ORDER BY sort ASC,create_time ASC","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":3,"fetchRowHistogram":[0,1]},{"sql":"SELECT   group_id   FROM twin_class      WHERE  (group_id IN (?,?,?) AND status = ?)","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":9,"fetchRowCountMax":9,"fetchRowHistogram":[0,1]},{"sql":"SELECT  id,model_id,model_type,type,title,version,size,file_exist,file_size,dir_flag,tags,classify,status,create_time,create_user,update_time,update_user  FROM things_model      WHERE  (model_id = ?)","executeCount":2,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]}]}
[1;35m10:40:20.834[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:40:21.000[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:40:21.000[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:40:21.000[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:40:21.001[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:40:21.001[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:40:21.001[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:40:21.001[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:40:21.001[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:40:21.001[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:40:21.002[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:40:21.002[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:40:21.002[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:40:21.003[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:40:21.003[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:40:21.003[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:40:21.004[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:40:21.004[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:40:21.004[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:40:21.004[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:40:21.005[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:40:21.005[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:40:21.005[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:40:21.092[0;39m [32m[http-nio-10007-exec-4][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,206][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:40:21.095[0;39m [32m[log-manager-factory6][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:40:39.624[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  table_name AS 'tableName',table_rows AS 'rowCount' FROM information_schema.TABLES WHERE  table_schema = ?]
Result:   [SELECT table_name AS 'tableName', table_rows AS 'rowCount' FROM all_tables WHERE owner = ?]
[1;35m10:40:39.976[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:scene-x, group:DEFAULT_GROUP, clusters: 
[1;35m10:40:39.976[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:scene-x, group:DEFAULT_GROUP, cluster: 
[1;35m10:40:39.984[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(2) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:40:39.985[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m10:40:40.026[0;39m [32m[http-nio-10007-exec-5][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - ThingsModelController#getOneByModelId proceed time: 8ms
[1;35m10:40:40.104[0;39m [32m[http-nio-10007-exec-6][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - ThingsModelController#getOneByModelId proceed time: 7ms
[1;35m10:40:40.188[0;39m [32m[http-nio-10007-exec-3][0;39m [34mINFO [0;39m [36mc.u.x.c.c.a.BusinessLogAdvice[0;39m - [36m[businessLog,83][0;39m - TwinClassController#page proceed time: 720ms
[1;35m10:40:40.586[0;39m [32m[nacos-grpc-client-executor-***********-170][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7227
[1;35m10:40:40.587[0;39m [32m[nacos-grpc-client-executor-***********-170][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7227
[1;35m10:40:43.928[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:41:22.861[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:40:21","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:40:21","connectCount":4,"closeCount":4,"executeCount":4,"pstmtCacheHitCount":4,"connectionHoldTimeHistogram":[0,4],"sqlList":[{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":4,"executeMillisMax":4,"executeMillisTotal":7,"executeHistogram":[0,4],"executeAndResultHoldHistogram":[0,4],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":1,"fetchRowHistogram":[0,4]}]}
[1;35m10:41:22.862[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:41:22.863[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:40:39","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:40:39","connectCount":1,"closeCount":1,"executeCount":1,"pstmtCacheMissCount":1,"connectionHoldTimeHistogram":[0,1],"clobOpenCount":4,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT id,group_id,name,img,file,type,flag,scal,img_suffix,attr,sort,decorate_url,status,create_time,create_user,update_time,update_user FROM sys_bubble_info WHERE id IN (   ?  ,  ?  ,  ?  ,  ?  )","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":4,"fetchRowCountMax":4,"fetchRowHistogram":[0,1],"clobOpenCount":4}]}
[1;35m10:41:22.862[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":12,"activePeak":12,"activePeakTime":"2025-08-25 10:40:45","poolingCount":0,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:40:20","connectCount":33,"closeCount":30,"notEmptyWaitCount":2,"notEmptyWaitMillis":27,"physicalConnectCount":2,"executeCount":30,"errorCount":2,"pstmtCacheHitCount":16,"pstmtCacheMissCount":14,"connectionHoldTimeHistogram":[1,25,3,1],"clobOpenCount":40,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[1,1],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":16,"executeMillisMax":7,"executeMillisTotal":56,"executeHistogram":[0,16],"executeAndResultHoldHistogram":[0,16],"concurrentMax":1,"fetchRowCount":16,"fetchRowCountMax":1,"fetchRowHistogram":[0,16]},{"sql":"SELECT id,name,code,group_id,level,data_type,custom_color,bubble_info_id,things_model_id,things_model_uuid,status,form,structure,pure,create_time,create_user,update_time,update_user FROM twin_class WHERE id=?","executeCount":2,"executeMillisMax":0,"executeMillisTotal":1,"executeHistogram":[2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2],"clobOpenCount":4},{"sql":"SELECT  id,twin_coalesce_id,twin_code,twin_coalesce_model,twin_model,status,create_time,create_user,update_time,update_user  FROM twin_coalesce_map      WHERE  (twin_code = ? AND twin_model IS NOT NULL AND status = ?)","executeCount":2,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1},{"sql":"SELECT id, twin_class_id, twin_class_code, field_name, \"condition\", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC","executeCount":2,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[1,1],"concurrentMax":1},{"sql":"drop table if exists \"xqjii_scenex\".\"tmp_twin_zhu\"\n;\nCREATE TABLE \"xqjii_scenex\".\"tmp_twin_zhu\" (\n  \"uuid\" BIGINT NOT NULL,\n  \"scene_id\" VARCHAR(127),\n  \"wgs84_position\" CLOB,\n  \"gcj02_position\" CLOB,\n  \"gis_height\" VARCHAR(255),\n  \"position_body\" CLOB,\n  \"data_source\" VARCHAR(255),\n  \"parent_user_id\" VARCHAR(127),\n  \"user_room_id\" VARCHAR(127),\n  \"position\" VARCHAR(255),\n  \"current_level\" VARCHAR(127),\n  \"user_floor_id\" VARCHAR(127),\n  \"user_building_id\" VARCHAR(127),\n  \"UniqueCode\" VARCHAR(255),\n  \"create_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"create_user\" VARCHAR(127),\n  \"update_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"update_user\" VARCHAR(127),\n  \"LAIZHEHEREN\" VARCHAR(255)\n,  PRIMARY KEY (\"uuid\")\n)\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"uuid\" IS '唯一标识'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"scene_id\" IS '所属场景'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"wgs84_position\" IS 'WGS84坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gcj02_position\" IS 'GCJ02火星坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gis_height\" IS '离地高度'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position_body\" IS '点位数据信息'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"data_source\" IS '点位数据来源'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"parent_user_id\" IS '场景层级中的物体id'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_room_id\" IS '所属房间ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position\" IS '位置坐标'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"current_level\" IS '所在层级'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_floor_id\" IS '所属楼层ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_building_id\" IS '所属建筑ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"UniqueCode\" IS '孪生体编码'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_time\" IS '创建时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_user\" IS '创建人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_time\" IS '更新时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_user\" IS '更新人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"LAIZHEHEREN\" IS '来者何人'\n;\nCREATE UNIQUE INDEX \"LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37\" ON \"xqjii_scenex\".\"tmp_twin_zhu\" (\"LAIZHEHEREN\")","executeCount":1,"executeMillisMax":85,"executeMillisTotal":85,"executeHistogram":[0,0,1],"executeAndResultHoldHistogram":[0,0,1],"executeErrorCount":1,"concurrentMax":1},{"sql":"SELECT  id,model_id,model_type,type,title,version,size,file_exist,file_size,dir_flag,tags,classify,status,create_time,create_user,update_time,update_user  FROM things_model      WHERE  (model_id = ?)","executeCount":2,"executeMillisMax":1,"executeMillisTotal":2,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"SELECT   code,id,bubble_info_id,things_model_uuid,things_model_id   FROM twin_class      WHERE  (status <> ? AND status = ?) /*+ MAX_EXECUTION_TIME(5000) */","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":9,"fetchRowCountMax":9,"fetchRowHistogram":[0,1]},{"sql":"SELECT COUNT(*) AS total FROM twin_class AS a WHERE a.code IN (?, ?, ?, ?, ?, ?, ?, ?, ?) AND a.status != 2","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( select\n        a.*,\n        0 as total\n        from twin_class as a\n         WHERE a.code in\n            (\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            )\n            and a.status != 2 \n         \n         \n            order by a.id desc ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?","executeCount":1,"executeMillisMax":14,"executeMillisTotal":14,"executeHistogram":[0,0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":9,"fetchRowCountMax":9,"fetchRowHistogram":[0,1],"clobOpenCount":18},{"sql":"SELECT COUNT(*) AS total FROM twin_class AS a LEFT JOIN (SELECT ? AS code, count(*) AS total FROM twin_LEGEND UNION ALL SELECT ? AS code, count(*) AS total FROM twin_MAP_LEVEL UNION ALL SELECT ? AS code, count(*) AS total FROM twin_FLOOR UNION ALL SELECT ? AS code, count(*) AS total FROM twin_BUILDING UNION ALL SELECT ? AS code, count(*) AS total FROM twin_ZHU UNION ALL SELECT ? AS code, count(*) AS total FROM twin_MENU UNION ALL SELECT ? AS code, count(*) AS total FROM twin_ROOM UNION ALL SELECT ? AS code, count(*) AS total FROM twin_MAP_VECTOR_DATA UNION ALL SELECT ? AS code, count(*) AS total FROM twin_PARK) AS b ON LOWER(a.code) = b.code WHERE a.code IN (?, ?, ?, ?, ?, ?, ?, ?, ?) AND a.status != 2","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( select\n        a.*,\n        b.total\n        from twin_class as a\n        left join\n         (  \n            select ? as code, count(*) as total from twin_LEGEND\n          union all  \n            select ? as code, count(*) as total from twin_MAP_LEVEL\n          union all  \n            select ? as code, count(*) as total from twin_FLOOR\n          union all  \n            select ? as code, count(*) as total from twin_BUILDING\n          union all  \n            select ? as code, count(*) as total from twin_ZHU\n          union all  \n            select ? as code, count(*) as total from twin_MENU\n          union all  \n            select ? as code, count(*) as total from twin_ROOM\n          union all  \n            select ? as code, count(*) as total from twin_MAP_VECTOR_DATA\n          union all  \n            select ? as code, count(*) as total from twin_PARK\n         ) \n        as b on LOWER(a.code) = b.code\n         WHERE a.code in\n            (\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            ,\n                ?\n            )\n            and a.status != 2 \n         \n         \n            order by id desc ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":9,"fetchRowCountMax":9,"fetchRowHistogram":[0,1],"clobOpenCount":18}]}
[1;35m10:41:22.868[0;39m [32m[nacos-grpc-client-executor-***********-173][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = ClientDetectionRequest, requestId = 7228
[1;35m10:41:22.869[0;39m [32m[nacos-grpc-client-executor-***********-173][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = ClientDetectionRequest, requestId = 7228
[1;35m10:41:22.869[0;39m [32m[nacos-grpc-client-executor-***********-184][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 7229
[1;35m10:41:22.869[0;39m [32m[nacos-grpc-client-executor-***********-184][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 7229
[1;35m10:41:22.872[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:41:22.882[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:41:22.885[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:41:22.889[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:41:22.891[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:41:22.894[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:41:22.897[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:41:22.900[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:41:22.904[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:41:22.907[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:41:22.910[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:41:22.914[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:41:22.917[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:41:22.920[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:41:22.922[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:41:22.926[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:41:22.929[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:41:22.932[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:41:22.935[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:41:22.938[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:41:22.941[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:41:22.944[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:41:23.138[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Server healthy check fail, currentConnection = 1756089542445_**********_54948
[1;35m10:41:23.139[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:41:23.139[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:41:23.153[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Success to connect a server [***********:8848], connectionId = 1756089682974_**********_55579
[1;35m10:41:23.153[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Abandon prev connection, server is ***********:8848, connectionId is 1756089542445_**********_54948
[1;35m10:41:23.153[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089542445_**********_54948
[1;35m10:41:23.154[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:41:23.154[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify disconnected event to listeners
[1;35m10:41:23.154[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:41:23.154[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify connected event to listeners.
[1;35m10:41:23.154[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m10:41:23.167[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Success to connect a server [***********:8848], connectionId = 1756089682988_**********_55581
[1;35m10:41:23.167[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Abandon prev connection, server is ***********:8848, connectionId is 1756089682974_**********_55579
[1;35m10:41:23.167[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089682974_**********_55579
[1;35m10:41:23.167[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify disconnected event to listeners
[1;35m10:41:23.168[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Notify connected event to listeners.
[1;35m10:41:23.168[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m10:41:23.274[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Server healthy check fail, currentConnection = 1756089542441_**********_54946
[1;35m10:41:23.287[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:41:23.288[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:41:25.032[0;39m [32m[http-nio-10007-exec-7][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,206][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:41:25.036[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Success to connect a server [***********:8848], connectionId = 1756089684855_**********_55593
[1;35m10:41:25.036[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Abandon prev connection, server is ***********:8848, connectionId is 1756089542441_**********_54946
[1;35m10:41:25.036[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089542441_**********_54946
[1;35m10:41:25.037[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:41:25.037[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify disconnected event to listeners
[1;35m10:41:25.037[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onDisConnect,720][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] DisConnected,clear listen context...
[1;35m10:41:25.037[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:41:25.037[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify connected event to listeners.
[1;35m10:41:25.037[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Connected,notify listen context...
[1;35m10:41:25.040[0;39m [32m[log-manager-factory9][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:41:25.052[0;39m [32m[log-manager-factory9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m10:41:25.053[0;39m [32m[log-manager-factory9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m10:41:25.055[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Success to connect a server [***********:8848], connectionId = 1756089684874_**********_55595
[1;35m10:41:25.055[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Abandon prev connection, server is ***********:8848, connectionId is 1756089684855_**********_55593
[1;35m10:41:25.056[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089684855_**********_55593
[1;35m10:41:25.056[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify disconnected event to listeners
[1;35m10:41:25.056[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onDisConnect,720][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] DisConnected,clear listen context...
[1;35m10:41:25.056[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify connected event to listeners.
[1;35m10:41:25.056[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Connected,notify listen context...
[1;35m10:41:25.655[0;39m [32m[nacos-grpc-client-executor-***********-196][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7232
[1;35m10:41:25.655[0;39m [32m[nacos-grpc-client-executor-***********-196][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7232
[1;35m10:41:25.861[0;39m [32m[com.alibaba.nacos.client.naming.grpc.redo.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[redoForInstance,73][0;39m - Redo instance operation REGISTER for DEFAULT_GROUP@@scene-x
[1;35m10:41:25.868[0;39m [32m[com.alibaba.nacos.client.naming.grpc.redo.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[redoForSubscribe,121][0;39m - Redo subscriber operation REGISTER for DEFAULT_GROUP@@scene-x#
[1;35m10:41:25.874[0;39m [32m[com.alibaba.nacos.client.naming.grpc.redo.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[redoForSubscribe,121][0;39m - Redo subscriber operation REGISTER for DEFAULT_GROUP@@system-x#
[1;35m10:41:26.458[0;39m [32m[nacos-grpc-client-executor-***********-203][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7233
[1;35m10:41:26.458[0;39m [32m[nacos-grpc-client-executor-***********-203][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7233
[1;35m10:41:26.460[0;39m [32m[nacos-grpc-client-executor-***********-204][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = NotifySubscriberRequest, requestId = 7235
[1;35m10:41:26.460[0;39m [32m[nacos-grpc-client-executor-***********-204][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = NotifySubscriberRequest, requestId = 7235
[1;35m10:41:44.936[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [SELECT  id,twin_class_id,twin_class_code,field_name,`condition`,content,bubble_info_id,bubble_info_name,order_num,things_model_uuid,status,create_time,create_user,update_time,update_user  FROM twin_class_model_mapping      WHERE  (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
Result:   [SELECT id, twin_class_id, twin_class_code, field_name, "condition", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC]
[1;35m10:42:15.037[0;39m [32m[nacos-grpc-client-executor-***********-211][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Receive server push request, request = ClientDetectionRequest, requestId = 7238
[1;35m10:42:15.038[0;39m [32m[nacos-grpc-client-executor-***********-211][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [e4b18e31-0b5a-4d28-ad9c-4539100e2819] Ack server push request, request = ClientDetectionRequest, requestId = 7238
[1;35m10:42:15.038[0;39m [32m[nacos-grpc-client-executor-***********-213][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 7237
[1;35m10:42:15.038[0;39m [32m[nacos-grpc-client-executor-***********-213][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 7237
[1;35m10:42:15.039[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: drop table if exists "xqjii_scenex"."tmp_twin_zhu"
[1;35m10:42:15.040[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE TABLE "xqjii_scenex"."tmp_twin_zhu" (
  "uuid" BIGINT NOT NULL,
  "scene_id" VARCHAR(127),
  "wgs84_position" CLOB,
  "gcj02_position" CLOB,
  "gis_height" VARCHAR(255),
  "position_body" CLOB,
  "data_source" VARCHAR(255),
  "parent_user_id" VARCHAR(127),
  "user_room_id" VARCHAR(127),
  "position" VARCHAR(255),
  "current_level" VARCHAR(127),
  "user_floor_id" VARCHAR(127),
  "user_building_id" VARCHAR(127),
  "UniqueCode" VARCHAR(255),
  "create_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "create_user" VARCHAR(127),
  "update_time" TIMESTAMP(0) DEFAULT current_timestamp,
  "update_user" VARCHAR(127),
  "LAIZHEHEREN" VARCHAR(255)
,  PRIMARY KEY ("uuid")
)
[1;35m10:42:15.041[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."uuid" IS '唯一标识'
[1;35m10:42:15.041[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."scene_id" IS '所属场景'
[1;35m10:42:15.041[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."wgs84_position" IS 'WGS84坐标系'
[1;35m10:42:15.041[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gcj02_position" IS 'GCJ02火星坐标系'
[1;35m10:42:15.041[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."gis_height" IS '离地高度'
[1;35m10:42:15.042[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position_body" IS '点位数据信息'
[1;35m10:42:15.042[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."data_source" IS '点位数据来源'
[1;35m10:42:15.042[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."parent_user_id" IS '场景层级中的物体id'
[1;35m10:42:15.042[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_room_id" IS '所属房间ID'
[1;35m10:42:15.042[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."position" IS '位置坐标'
[1;35m10:42:15.043[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."current_level" IS '所在层级'
[1;35m10:42:15.043[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_floor_id" IS '所属楼层ID'
[1;35m10:42:15.043[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."user_building_id" IS '所属建筑ID'
[1;35m10:42:15.044[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."UniqueCode" IS '孪生体编码'
[1;35m10:42:15.044[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_time" IS '创建时间'
[1;35m10:42:15.044[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."create_user" IS '创建人'
[1;35m10:42:15.045[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_time" IS '更新时间'
[1;35m10:42:15.046[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."update_user" IS '更新人'
[1;35m10:42:15.046[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: COMMENT ON COLUMN "xqjii_scenex"."tmp_twin_zhu"."LAIZHEHEREN" IS '来者何人'
[1;35m10:42:15.046[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.c.d.u.SqlUtils[0;39m - [36m[addBatchIfNotEmpty,830][0;39m - Batch add sql: CREATE UNIQUE INDEX "LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37" ON "xqjii_scenex"."tmp_twin_zhu" ("LAIZHEHEREN")
[1;35m10:42:15.112[0;39m [32m[http-nio-10007-exec-8][0;39m [34mINFO [0;39m [36mc.u.x.p.t.i.d.AlterJrmDdl[0;39m - [36m[generateTempTable,206][0;39m - 临时表生成失败: tmp_twin_zhu
[1;35m10:42:15.113[0;39m [32m[log-manager-factory10][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[transform,357][0;39m - SQL transformation completed in engine [dm].
Original: [select count(*) from information_schema.schemata where schema_name = ?;]
Result:   [SELECT count(*) FROM dba_objects WHERE object_name = ?;]
[1;35m10:42:15.146[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Server healthy check fail, currentConnection = 1756089684874_**********_55595
[1;35m10:42:15.146[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:42:15.147[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:42:15.163[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Success to connect a server [***********:8848], connectionId = 1756089734982_**********_55794
[1;35m10:42:15.163[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Abandon prev connection, server is ***********:8848, connectionId is 1756089684874_**********_55595
[1;35m10:42:15.163[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089684874_**********_55595
[1;35m10:42:15.163[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[1;35m10:42:15.163[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify disconnected event to listeners
[1;35m10:42:15.163[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onDisConnect,720][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] DisConnected,clear listen context...
[1;35m10:42:15.163[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m10:42:15.164[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify connected event to listeners.
[1;35m10:42:15.164[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Connected,notify listen context...
[1;35m10:42:15.182[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Success to connect a server [***********:8848], connectionId = 1756089734999_**********_55796
[1;35m10:42:15.182[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Abandon prev connection, server is ***********:8848, connectionId is 1756089734982_**********_55794
[1;35m10:42:15.182[0;39m [32m[com.alibaba.nacos.client.remote.worker.1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756089734982_**********_55794
[1;35m10:42:15.182[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify disconnected event to listeners
[1;35m10:42:15.182[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onDisConnect,720][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] DisConnected,clear listen context...
[1;35m10:42:15.182[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Notify connected event to listeners.
[1;35m10:42:15.182[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [842870ad-c0a8-4c2b-9b38-d75a03f83f65_config-0] Connected,notify listen context...
[1;35m10:42:22.866[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"systemx_1959806374981775362","activeCount":0,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m10:42:22.866[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"scenex","activeCount":1,"poolingCount":3,"connectCount":0,"closeCount":0}
[1;35m10:42:22.866[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_scenex&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_scenex_1959806378127503362","activeCount":12,"activePeak":13,"activePeakTime":"2025-08-25 10:41:45","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:41:25","connectCount":10,"closeCount":10,"notEmptyWaitCount":1,"notEmptyWaitMillis":13,"physicalConnectCount":1,"executeCount":10,"errorCount":2,"pstmtCacheHitCount":3,"pstmtCacheMissCount":5,"connectionHoldTimeHistogram":[0,7,1,0,0,2],"clobOpenCount":2,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT count(*) FROM dba_objects WHERE object_name = ?;","executeCount":5,"executeMillisMax":4,"executeMillisTotal":16,"executeHistogram":[0,5],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"fetchRowCount":5,"fetchRowCountMax":1,"fetchRowHistogram":[0,5]},{"sql":"SELECT id,name,code,group_id,level,data_type,custom_color,bubble_info_id,things_model_id,things_model_uuid,status,form,structure,pure,create_time,create_user,update_time,update_user FROM twin_class WHERE id=?","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1],"clobOpenCount":2},{"sql":"SELECT  id,twin_coalesce_id,twin_code,twin_coalesce_model,twin_model,status,create_time,create_user,update_time,update_user  FROM twin_coalesce_map      WHERE  (twin_code = ? AND twin_model IS NOT NULL AND status = ?)","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1},{"sql":"SELECT id, twin_class_id, twin_class_code, field_name, \"condition\", content, bubble_info_id, bubble_info_name, order_num, things_model_uuid, status, create_time, create_user, update_time, update_user FROM twin_class_model_mapping WHERE (twin_class_id = ? AND status <> ?) ORDER BY order_num ASC","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1},{"sql":"drop table if exists \"xqjii_scenex\".\"tmp_twin_zhu\"\n;\nCREATE TABLE \"xqjii_scenex\".\"tmp_twin_zhu\" (\n  \"uuid\" BIGINT NOT NULL,\n  \"scene_id\" VARCHAR(127),\n  \"wgs84_position\" CLOB,\n  \"gcj02_position\" CLOB,\n  \"gis_height\" VARCHAR(255),\n  \"position_body\" CLOB,\n  \"data_source\" VARCHAR(255),\n  \"parent_user_id\" VARCHAR(127),\n  \"user_room_id\" VARCHAR(127),\n  \"position\" VARCHAR(255),\n  \"current_level\" VARCHAR(127),\n  \"user_floor_id\" VARCHAR(127),\n  \"user_building_id\" VARCHAR(127),\n  \"UniqueCode\" VARCHAR(255),\n  \"create_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"create_user\" VARCHAR(127),\n  \"update_time\" TIMESTAMP(0) DEFAULT current_timestamp,\n  \"update_user\" VARCHAR(127),\n  \"LAIZHEHEREN\" VARCHAR(255)\n,  PRIMARY KEY (\"uuid\")\n)\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"uuid\" IS '唯一标识'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"scene_id\" IS '所属场景'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"wgs84_position\" IS 'WGS84坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gcj02_position\" IS 'GCJ02火星坐标系'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"gis_height\" IS '离地高度'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position_body\" IS '点位数据信息'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"data_source\" IS '点位数据来源'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"parent_user_id\" IS '场景层级中的物体id'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_room_id\" IS '所属房间ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"position\" IS '位置坐标'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"current_level\" IS '所在层级'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_floor_id\" IS '所属楼层ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"user_building_id\" IS '所属建筑ID'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"UniqueCode\" IS '孪生体编码'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_time\" IS '创建时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"create_user\" IS '创建人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_time\" IS '更新时间'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"update_user\" IS '更新人'\n;\nCOMMENT ON COLUMN \"xqjii_scenex\".\"tmp_twin_zhu\".\"LAIZHEHEREN\" IS '来者何人'\n;\nCREATE UNIQUE INDEX \"LAIZHEHEREN_UNIQUE_2efe12c658e14d3ca6ab4d33992b4b37\" ON \"xqjii_scenex\".\"tmp_twin_zhu\" (\"LAIZHEHEREN\")","executeCount":2,"executeMillisMax":341,"executeMillisTotal":407,"executeHistogram":[0,0,1,1],"executeAndResultHoldHistogram":[0,0,1,1],"executeErrorCount":2,"concurrentMax":1}]}
[1;35m10:42:22.870[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"jdbc:dm://***********:5236?schema=xqjii_systemx&compatibleMode=mysql&ignoreCase=true&ENCODING=utf-8","dbType":"dm","name":"xqjii_systemx_1959806601352556545","activeCount":0,"activePeak":1,"activePeakTime":"2025-08-25 10:41:25","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 10:41:25","connectCount":2,"closeCount":2,"executeCount":2,"pstmtCacheHitCount":2,"connectionHoldTimeHistogram":[0,2],"sqlList":[{"sql":"SELECT  id,account,password,password_strength,password_period,nick_name,name,avatar,birthday,sex,email,phone,tel,last_login_ip,last_login_time,admin_type,status,user_unique_value,security_question,security_answer,create_time,token_switch,third_party_login,ldap_account,secret_key,password_period_temp,create_user,update_time,update_user  FROM sys_user      WHERE  (account = ? AND status <> ?)","executeCount":2,"executeMillisMax":2,"executeMillisTotal":3,"executeHistogram":[1,1],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]}]}
[1;35m11:10:52.428[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m11:10:52.490[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m11:10:52.591[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m11:10:52.671[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m11:10:52.679[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SceneXApplication[0;39m - [36m[logStarting,53][0;39m - Starting SceneXApplication using Java 17.0.14 with PID 29976 (D:\x\pedestal-x\pedestal-x-app\scene-x\target\classes started by Administrator in D:\x)
[1;35m11:10:52.681[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SceneXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal-x"
[1;35m11:10:52.770[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m11:10:52.771[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m11:10:52.771[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=scene-x.yml, group=DEFAULT_GROUP] success
[1;35m11:10:54.857[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.effectpackage:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.860[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.file:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.866[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.868[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.870[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.871[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.873[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.874[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.876[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.878[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.879[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.880[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.882[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.883[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.885[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.887[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.888[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.890[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.891[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.892[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.892[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.894[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.895[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.896[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.896[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.897[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.897[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.898[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.898[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.900[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.901[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.901[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.903[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.903[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.905[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.906[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.909[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.bubble:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.910[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.model:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.913[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.tenant:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.918[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.projection:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.918[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.projection:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.921[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.922[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.924[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.926[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.930[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.932[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.934[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.939[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.941[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.943[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:54.950[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.config:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m11:10:55.789[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m11:10:55.799[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[1;35m11:10:55.854[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 35 ms. Found 0 Elasticsearch repository interfaces.
[1;35m11:10:55.867[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m11:10:55.869[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[1;35m11:10:55.886[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
[1;35m11:10:55.903[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m11:10:55.904[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m11:10:55.931[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[1;35m11:10:56.596[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=3af7533c-4f68-332a-8890-2cc84d4b3928
[1;35m11:10:56.640[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[registerStrategies,294][0;39m - No strategies provided for registration in engine [mysql]
[1;35m11:10:56.641[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[<init>,235][0;39m - SQL transformation engine [mysql] initialized with 0 strategies
[1;35m11:10:56.724[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-1} inited
[1;35m11:11:12.799[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-1} closing ...
[1;35m11:11:12.802[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-1} closed
[1;35m11:11:14.430[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 10007 (http)
[1;35m11:11:14.468[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-10007"]
[1;35m11:11:14.471[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m11:11:14.471[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.43]
[1;35m11:11:14.586[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m11:11:14.587[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 21812 ms
[1;35m11:11:16.489[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m11:11:16.703[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [scenex] success
[1;35m11:11:16.704[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[afterPropertiesSet,241][0;39m - dynamic-datasource initial loaded [1] datasource,primary datasource named [scenex]
[1;35m11:11:32.434[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-2,scenex} inited
[1;35m11:11:32.436[0;39m [32m[Druid-ConnectionPool-Log-*********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"************************************************************************************************************************************************************************","dbType":"mysql","name":"scenex","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-25 11:11:32","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1}]}
[1;35m11:11:34.735[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m11:11:35.432[0;39m [32m[redisson-netty-1-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for 10.203.3.40/10.203.3.40:6379
[1;35m11:11:35.457[0;39m [32m[redisson-netty-1-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for 10.203.3.40/10.203.3.40:6379
[1;35m11:11:51.417[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: ************************************ (MySQL 8.0)
[1;35m11:11:51.672[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[destroy,215][0;39m - dynamic-datasource start closing ....
[1;35m11:11:51.675[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-2} closing ...
[1;35m11:11:51.677[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-2} closed
[1;35m11:11:51.677[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.d.DefaultDataSourceDestroyer[0;39m - [36m[destroy,98][0;39m - dynamic-datasource close the datasource named [scenex] success,
[1;35m11:11:51.677[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[destroy,219][0;39m - dynamic-datasource all closed success,bye
[1;35m11:11:51.687[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Stopping service [Tomcat]
[1;35m11:11:51.730[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.l.ConditionEvaluationReportLogger[0;39m - [36m[logMessage,82][0;39m - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
