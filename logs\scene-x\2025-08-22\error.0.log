[1;35m10:28:23.904[0;39m [32m[main][0;39m [1;31mERROR[0;39m [36mo.s.b.SpringApplication[0;39m - [36m[reportFailure,857][0;39m - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'initConfigRunner' defined in file [D:\x\pedestal-x\pedestal-x-app\scene-x\target\classes\com\uino\x\pedestal\app\scenex\config\InitConfigRunner.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'tenantInfoMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\tenant\tenant-dao\target\classes\com\uino\x\pedestal\tenant\dao\mapper\TenantInfoMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.11.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.06.20.11.12.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.02.17.07.07.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.21.10.24.24.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at com.uino.x.pedestal.app.scenex.SceneXApplication.main(SceneXApplication.java:34)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tenantInfoMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\tenant\tenant-dao\target\classes\com\uino\x\pedestal\tenant\dao\mapper\TenantInfoMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionTemplate': Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.11.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.06.20.11.12.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.02.17.07.07.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.21.10.24.24.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1450)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1683)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'flywayInitializer' defined in class path resource [org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class]: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.11.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.06.20.11.12.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.02.17.07.07.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.21.10.24.24.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1826)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:315)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1541)
	... 30 common frames omitted
Caused by: org.flywaydb.core.api.exception.FlywayValidateException: Validate failed: Migrations have failed validation
Detected resolved migration not applied to database: 2025.***********.11.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.***********.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.06.20.11.12.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.02.17.07.07.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Detected resolved migration not applied to database: 2025.07.21.10.24.24.
To ignore this migration, set -ignoreMigrationPatterns='*:ignored'. To allow executing this migration, set -outOfOrder=true.
Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:201)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:210)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:188)
	at com.uino.x.common.redis.redisson.FlywayRedissonMigrationStrategy.migrate(FlywayRedissonMigrationStrategy.java:37)
	at org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer.afterPropertiesSet(FlywayMigrationInitializer.java:62)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	... 42 common frames omitted
[1;35m16:41:01.879[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[servletException,303][0;39m - >>> servlet 异常
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at apijson.orm.AbstractParser.<init>(AbstractParser.java:548)
	at com.uino.x.pedestal.twin.jrm.apijson.JrmParser.<init>(JrmParser.java:23)
	at com.uino.x.pedestal.twin.jrm.Jrm.request(Jrm.java:168)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:65)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:40)
	at com.uino.x.pedestal.twin.impl.JrmServiceImpl.get(JrmServiceImpl.java:57)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController.get(JrmController.java:44)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController$$SpringCGLIB$$0.get(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 59 common frames omitted
[1;35m16:41:01.890[0;39m [32m[http-nio-10007-exec-2][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at apijson.orm.AbstractParser.<init>(AbstractParser.java:548)
	at com.uino.x.pedestal.twin.jrm.apijson.JrmParser.<init>(JrmParser.java:23)
	at com.uino.x.pedestal.twin.jrm.Jrm.request(Jrm.java:168)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:65)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:40)
	at com.uino.x.pedestal.twin.impl.JrmServiceImpl.get(JrmServiceImpl.java:57)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController.get(JrmController.java:44)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController$$SpringCGLIB$$0.get(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 59 common frames omitted
[1;35m16:41:08.139[0;39m [32m[http-nio-10007-exec-3][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[servletException,303][0;39m - >>> servlet 异常
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at apijson.orm.AbstractParser.<init>(AbstractParser.java:548)
	at com.uino.x.pedestal.twin.jrm.apijson.JrmParser.<init>(JrmParser.java:23)
	at com.uino.x.pedestal.twin.jrm.Jrm.request(Jrm.java:168)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:65)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:40)
	at com.uino.x.pedestal.twin.impl.JrmServiceImpl.get(JrmServiceImpl.java:57)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController.get(JrmController.java:44)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController$$SpringCGLIB$$0.get(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 59 common frames omitted
[1;35m16:41:08.141[0;39m [32m[http-nio-10007-exec-3][0;39m [1;31mERROR[0;39m [36mc.u.x.c.c.h.BasicGlobalExceptionHandler[0;39m - [36m[serverError,369][0;39m - >>> 服务器运行异常
jakarta.servlet.ServletException: Handler dispatch failed: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1104)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.uino.x.common.config.CommonXLogConfig.lambda$0(CommonXLogConfig.java:100)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.Error: Unresolved compilation problems: 
	The field KEY_ROLE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_CACHE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_NAMESPACE is ambiguous
	The field KEY_CATALOG is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_COLUMN is ambiguous
	The field KEY_NULL is ambiguous
	The field KEY_CAST is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_GROUP is ambiguous
	The field KEY_HAVING is ambiguous
	The field KEY_HAVING_AND is ambiguous
	The field KEY_SAMPLE is ambiguous
	The field KEY_LATEST is ambiguous
	The field KEY_PARTITION is ambiguous
	The field KEY_FILL is ambiguous
	The field KEY_ORDER is ambiguous
	The field KEY_KEY is ambiguous
	The field KEY_RAW is ambiguous
	The field KEY_EXPLAIN is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_GET is ambiguous
	The field KEY_GETS is ambiguous
	The field KEY_HEAD is ambiguous
	The field KEY_HEADS is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_PUT is ambiguous
	The field KEY_DELETE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_POST is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_DATASOURCE is ambiguous
	The field KEY_SCHEMA is ambiguous
	The field KEY_DATABASE is ambiguous
	The field KEY_ROLE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_COMBINE is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_METHOD is ambiguous
	The field KEY_ARRAY is ambiguous
	The field KEY_METHOD is ambiguous

	at apijson.orm.AbstractParser.<init>(AbstractParser.java:548)
	at com.uino.x.pedestal.twin.jrm.apijson.JrmParser.<init>(JrmParser.java:23)
	at com.uino.x.pedestal.twin.jrm.Jrm.request(Jrm.java:168)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:65)
	at com.uino.x.pedestal.twin.jrm.JrmBean.request(JrmBean.java:40)
	at com.uino.x.pedestal.twin.impl.JrmServiceImpl.get(JrmServiceImpl.java:57)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController.get(JrmController.java:44)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:360)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.uino.x.common.config.aop.BusinessLogAdvice.businessLog(BusinessLogAdvice.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:649)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:631)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.uino.x.pedestal.app.scenex.controller.twin.JrmController$$SpringCGLIB$$0.get(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:991)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:896)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	... 59 common frames omitted
