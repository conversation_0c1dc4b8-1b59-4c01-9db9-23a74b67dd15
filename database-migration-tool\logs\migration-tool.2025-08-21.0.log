2025-08-21 14:14:26.585 [main] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-21 14:14:26.613 [main] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - Created 9 default transformation strategies
2025-08-21 14:14:26.681 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 9 transformation strategies in engine [Test-Engine]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, BacktickQuoteTransformationStrategy]
2025-08-21 14:14:26.682 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [Test-Engine] initialized with 9 strategies
2025-08-21 14:14:26.687 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation statistics: 
SqlTransformationEngine [Test-Engine] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 9 registered
  Memory: Estimated Size=0 bytes
2025-08-21 14:14:26.705 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [Test-Engine].
Original: [CREATE DATABASE test]
Result:   [CREATE SCHEMA test;]
2025-08-21 14:14:26.710 [main] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-21 14:14:26.710 [main] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - Created 9 default transformation strategies
2025-08-21 14:14:26.711 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 9 transformation strategies in engine [MySQL-to-Dameng]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, BacktickQuoteTransformationStrategy]
2025-08-21 14:14:26.711 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-Dameng] initialized with 9 strategies
2025-08-21 14:14:26.712 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation statistics: 
SqlTransformationEngine [MySQL-to-Dameng] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 9 registered
  Memory: Estimated Size=0 bytes
2025-08-21 14:14:26.797 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-Dameng].
Original: [CREATE DATABASE `test_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci]
Result:   [CREATE SCHEMA "test_db";]
2025-08-21 14:14:26.798 [main] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-21 14:14:26.798 [main] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - Created 9 default transformation strategies
2025-08-21 14:14:26.799 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 9 transformation strategies in engine [Backtick-Test]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, BacktickQuoteTransformationStrategy]
2025-08-21 14:14:26.800 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [Backtick-Test] initialized with 9 strategies
2025-08-21 14:14:26.801 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation statistics: 
SqlTransformationEngine [Backtick-Test] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 9 registered
  Memory: Estimated Size=0 bytes
2025-08-21 14:14:26.824 [main] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [Backtick-Test].
Original: [SELECT `id`, `name` FROM `users` WHERE `status` = 'active']
Result:   [SELECT "id", "name" FROM "users" WHERE "status" = 'active']
