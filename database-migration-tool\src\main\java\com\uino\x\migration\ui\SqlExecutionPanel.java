package com.uino.x.migration.ui;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.SwingUtilities;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.JTable;

import com.uino.x.migration.config.ConfigManager;
import com.uino.x.migration.config.DatabaseConfig;
import com.uino.x.migration.service.DatabaseService;

import lombok.extern.slf4j.Slf4j;

/**
 * SQL执行面板
 * 提供直接执行SQL语句的功能，与系统配置面板功能一致，但去掉固定的数据映射配置
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
public class SqlExecutionPanel extends JPanel {
    
    private final DatabaseService databaseService;
    private final CombinedDatabaseConfigPanel databaseConfigPanel;
    
    // UI组件
    private JComboBox<String> databaseConfigCombo;
    private JButton refreshConfigButton;
    private JTextArea sqlTextArea;
    private JButton executeButton;
    private JButton clearButton;
    private JButton saveConfigButton;
    private JProgressBar progressBar;
    private JTextArea logArea;
    private JTable resultTable;
    private DefaultTableModel tableModel;
    
    // 配置回调
    private Runnable configCallback;

    public SqlExecutionPanel(DatabaseService databaseService, CombinedDatabaseConfigPanel databaseConfigPanel) {
        this.databaseService = databaseService;
        this.databaseConfigPanel = databaseConfigPanel;
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        // 数据库配置选择
        databaseConfigCombo = new JComboBox<>();
        databaseConfigCombo.addItem("请选择数据库配置");
        refreshConfigButton = new JButton("刷新配置");
        
        // SQL输入区域
        sqlTextArea = new JTextArea(8, 50);
        sqlTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        sqlTextArea.setLineWrap(true);
        sqlTextArea.setWrapStyleWord(true);
        sqlTextArea.setTabSize(4);
        sqlTextArea.setText("-- 请在此输入SQL语句\nSELECT * FROM sys_config LIMIT 10;");

        executeButton = new JButton("执行SQL");
        clearButton = new JButton("清空");
        saveConfigButton = new JButton("保存配置");
        
        // 进度条和日志
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("就绪");
        
        logArea = new JTextArea(8, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        // 结果表格
        tableModel = new DefaultTableModel();
        resultTable = new JTable(tableModel);
        resultTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
        resultTable.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());

        // 创建主面板
        JPanel mainPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // 数据库配置选择
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        gbc.weighty = 0.0;
        mainPanel.add(createDatabaseConfigSelectionPanel(), gbc);

        // SQL输入区域
        gbc.gridy = 1;
        gbc.weighty = 0.3;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(createSqlInputPanel(), gbc);

        // 操作按钮
        gbc.gridy = 2;
        gbc.weighty = 0.0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(createButtonPanel(), gbc);

        // 结果显示区域
        gbc.gridy = 3;
        gbc.weighty = 0.3;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(createResultPanel(), gbc);

        // 进度和日志
        gbc.gridy = 4;
        gbc.weighty = 0.4;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(createProgressAndLogPanel(), gbc);

        add(mainPanel, BorderLayout.CENTER);
    }
    
    /**
     * 创建数据库配置选择面板
     */
    private JPanel createDatabaseConfigSelectionPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "数据库配置", 
            TitledBorder.LEFT, TitledBorder.TOP));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 数据库配置选择
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("数据库配置:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        panel.add(databaseConfigCombo, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(refreshConfigButton, gbc);
        
        return panel;
    }
    
    /**
     * 创建SQL输入面板
     */
    private JPanel createSqlInputPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "SQL输入", 
            TitledBorder.LEFT, TitledBorder.TOP));
        
        JScrollPane scrollPane = new JScrollPane(sqlTextArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.add(executeButton);
        panel.add(clearButton);
        panel.add(saveConfigButton);
        return panel;
    }
    
    /**
     * 创建结果显示面板
     */
    private JPanel createResultPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "执行结果", 
            TitledBorder.LEFT, TitledBorder.TOP));
        
        JScrollPane scrollPane = new JScrollPane(resultTable);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new Dimension(0, 200));
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建进度和日志面板
     */
    private JPanel createProgressAndLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "执行进度和日志", 
            TitledBorder.LEFT, TitledBorder.TOP));
        
        // 进度条
        panel.add(progressBar, BorderLayout.NORTH);
        
        // 日志区域
        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        panel.add(logScrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 刷新配置按钮
        refreshConfigButton.addActionListener(e -> refreshDatabaseConfigs());
        
        // 数据库配置选择变化
        databaseConfigCombo.addActionListener(e -> onDatabaseConfigChanged());
        
        // 执行按钮
        executeButton.addActionListener(e -> executeSql());
        
        // 清空按钮
        clearButton.addActionListener(e -> clearAll());
        
        // 保存配置按钮
        saveConfigButton.addActionListener(e -> saveCurrentConfig());
    }
    
    /**
     * 添加日志
     */
    private void addLog(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append("[" + java.time.LocalTime.now().toString() + "] " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }

    /**
     * 刷新数据库配置列表
     */
    private void refreshDatabaseConfigs() {
        databaseConfigCombo.removeAllItems();
        databaseConfigCombo.addItem("请选择数据库配置");

        // 获取所有命名配置
        java.util.Map<String, ConfigManager.NamedConfig> namedConfigs = ConfigManager.getAllNamedConfigs();
        for (String configName : namedConfigs.keySet()) {
            databaseConfigCombo.addItem(configName);
        }

        addLog("数据库配置列表已刷新");
    }

    /**
     * 数据库配置选择变化事件
     */
    private void onDatabaseConfigChanged() {
        String selectedConfigName = (String) databaseConfigCombo.getSelectedItem();
        if (selectedConfigName == null || "请选择数据库配置".equals(selectedConfigName)) {
            return;
        }

        try {
            ConfigManager.NamedConfig namedConfig = ConfigManager.getNamedConfig(selectedConfigName);
            if (namedConfig != null) {
                addLog("已选择数据库配置: " + selectedConfigName);
            }
        } catch (Exception e) {
            log.error("选择数据库配置失败", e);
            addLog("选择数据库配置失败: " + e.getMessage());
        }
    }

    /**
     * 执行SQL
     */
    private void executeSql() {
        // 获取数据库配置
        String selectedConfigName = (String) databaseConfigCombo.getSelectedItem();
        if (selectedConfigName == null || selectedConfigName.trim().isEmpty() || "请选择数据库配置".equals(selectedConfigName)) {
            JOptionPane.showMessageDialog(this,
                "请选择数据库配置",
                "配置错误",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 获取SQL语句
        String sql = sqlTextArea.getText();
        if (sql == null || sql.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "请输入SQL语句",
                "输入错误",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 在后台线程中执行
        SwingUtilities.invokeLater(() -> {
            executeButton.setEnabled(false);
            progressBar.setValue(0);
            progressBar.setString("正在执行SQL...");

            new Thread(() -> {
                try {
                    performSqlExecution(selectedConfigName, sql.trim());
                } finally {
                    SwingUtilities.invokeLater(() -> {
                        executeButton.setEnabled(true);
                        progressBar.setString("执行完成");
                    });
                }
            }).start();
        });
    }

    /**
     * 执行实际的SQL操作
     */
    private void performSqlExecution(String configName, String sql) {
        try {
            addLog("开始执行SQL...");
            addLog("配置: " + configName);
            addLog("SQL: " + sql);

            // 获取数据库配置
            ConfigManager.NamedConfig namedConfig = ConfigManager.getNamedConfig(configName);
            if (namedConfig == null) {
                addLog("错误: 无法找到配置 " + configName);
                return;
            }

            // 获取数据库连接
            DatabaseConfig sourceDb = namedConfig.getSourceDatabase();
            if (sourceDb == null) {
                addLog("错误: 配置中没有源数据库信息");
                return;
            }

            SwingUtilities.invokeLater(() -> {
                progressBar.setValue(20);
                progressBar.setString("正在连接数据库...");
            });

            try (Connection connection = databaseService.createDataSource(sourceDb).getConnection()) {
                addLog("数据库连接成功");

                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(40);
                    progressBar.setString("正在执行SQL...");
                });

                // 判断SQL类型并执行
                String sqlLower = sql.toLowerCase().trim();
                if (sqlLower.startsWith("select") || sqlLower.startsWith("show") || sqlLower.startsWith("desc") || sqlLower.startsWith("explain")) {
                    // 查询语句
                    executeQuery(connection, sql);
                } else {
                    // 更新语句
                    executeUpdate(connection, sql);
                }

                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(100);
                    progressBar.setString("SQL执行完成");
                });
                addLog("SQL执行完成！");

            } catch (SQLException e) {
                log.error("数据库操作失败", e);
                addLog("数据库操作失败: " + e.getMessage());

                // 弹框提示错误
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(SqlExecutionPanel.this,
                        "数据库操作失败：" + e.getMessage(),
                        "执行失败",
                        JOptionPane.ERROR_MESSAGE);
                });
            }

        } catch (Exception e) {
            log.error("执行SQL失败", e);
            addLog("执行失败: " + e.getMessage());

            // 弹框提示错误
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(SqlExecutionPanel.this,
                    "执行失败：" + e.getMessage(),
                    "执行失败",
                    JOptionPane.ERROR_MESSAGE);
            });
        }
    }

    /**
     * 执行查询语句
     */
    private void executeQuery(Connection connection, String sql) throws SQLException {
        try (Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery(sql)) {

            // 获取结果集元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            // 清空之前的结果
            SwingUtilities.invokeLater(() -> {
                tableModel.setRowCount(0);
                tableModel.setColumnCount(0);
            });

            // 设置列名
            String[] columnNames = new String[columnCount];
            for (int i = 1; i <= columnCount; i++) {
                columnNames[i - 1] = metaData.getColumnName(i);
            }

            SwingUtilities.invokeLater(() -> {
                tableModel.setColumnIdentifiers(columnNames);
            });

            // 添加数据行
            List<Object[]> rows = new ArrayList<>();
            int rowCount = 0;
            while (resultSet.next() && rowCount < 1000) { // 限制最多显示1000行
                Object[] row = new Object[columnCount];
                for (int i = 1; i <= columnCount; i++) {
                    row[i - 1] = resultSet.getObject(i);
                }
                rows.add(row);
                rowCount++;
            }

            SwingUtilities.invokeLater(() -> {
                for (Object[] row : rows) {
                    tableModel.addRow(row);
                }
            });

            addLog("查询完成，返回 " + rowCount + " 行数据");
            if (rowCount >= 1000) {
                addLog("注意：结果已限制为前1000行");
            }
        }
    }

    /**
     * 执行更新语句
     */
    private void executeUpdate(Connection connection, String sql) throws SQLException {
        try (Statement statement = connection.createStatement()) {
            int affectedRows = statement.executeUpdate(sql);

            // 清空结果表格
            SwingUtilities.invokeLater(() -> {
                tableModel.setRowCount(0);
                tableModel.setColumnCount(0);
                tableModel.setColumnIdentifiers(new String[]{"执行结果"});
                tableModel.addRow(new Object[]{"影响行数: " + affectedRows});
            });

            addLog("更新完成，影响 " + affectedRows + " 行");

            // 弹框提示完成
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(SqlExecutionPanel.this,
                    "SQL执行完成！\n影响行数: " + affectedRows,
                    "执行完成",
                    JOptionPane.INFORMATION_MESSAGE);
            });
        }
    }

    /**
     * 清空所有内容
     */
    private void clearAll() {
        sqlTextArea.setText("-- 请在此输入SQL语句\n");
        logArea.setText("");
        tableModel.setRowCount(0);
        tableModel.setColumnCount(0);
        progressBar.setValue(0);
        progressBar.setString("就绪");
        addLog("界面已清空");
    }

    /**
     * 设置配置回调
     */
    public void setConfigCallback(Runnable configCallback) {
        this.configCallback = configCallback;
    }

    /**
     * 初始化时刷新配置列表
     */
    public void initializeDatabaseConfigs() {
        refreshDatabaseConfigs();
    }

    /**
     * 保存当前配置
     */
    private void saveCurrentConfig() {
        // 直接保存到主配置，不弹框
        if (configCallback != null) {
            configCallback.run();
            JOptionPane.showMessageDialog(this,
                "SQL执行面板配置已保存",
                "保存成功",
                JOptionPane.INFORMATION_MESSAGE);
            log.info("SQL执行面板配置已保存");
        }
    }

    /**
     * 获取当前SQL执行配置
     */
    public ConfigManager.SqlExecutionConfig getSqlExecutionConfig() {
        ConfigManager.SqlExecutionConfig config = new ConfigManager.SqlExecutionConfig();

        // 设置选择的数据库配置
        String selectedConfig = (String) databaseConfigCombo.getSelectedItem();
        if (selectedConfig != null && !"请选择数据库配置".equals(selectedConfig)) {
            config.setSelectedDatabaseConfig(selectedConfig);
        }

        // 设置SQL内容
        config.setSqlContent(sqlTextArea.getText());

        return config;
    }

    /**
     * 设置SQL执行配置到UI组件
     */
    public void setSqlExecutionConfig(ConfigManager.SqlExecutionConfig config) {
        if (config == null) return;

        // 设置选择的数据库配置
        if (config.getSelectedDatabaseConfig() != null) {
            databaseConfigCombo.setSelectedItem(config.getSelectedDatabaseConfig());
            onDatabaseConfigChanged();
        }

        // 设置SQL内容
        if (config.getSqlContent() != null) {
            sqlTextArea.setText(config.getSqlContent());
        }
    }
}
