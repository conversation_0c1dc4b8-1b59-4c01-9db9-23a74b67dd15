package com.uino.x.migration.config;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 配置管理器
 * 负责保存和加载应用程序配置
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Slf4j
public class ConfigManager {
    
    private static final String CONFIG_DIR = System.getProperty("user.home") + File.separator + ".database-migration-tool";
    private static final String CONFIG_FILE = CONFIG_DIR + File.separator + "config.json";
    private static final ObjectMapper objectMapper = createObjectMapper();

    /**
     * 创建配置好的ObjectMapper
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        // 忽略未知字段，避免配置文件格式变化时出错
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 启用美化输出
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
        // 注册Java 8时间模块
        mapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());
        // 禁用将日期写为时间戳
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return mapper;
    }
    
    /**
     * 应用程序配置
     */
    @Data
    public static class AppConfig {
        // 命名数据库配置
        private java.util.Map<String, NamedConfig> namedConfigs = new java.util.HashMap<>();

        // 应用程序配置
        private MigrationConfig migrationConfig;
        private OneClickMigrationConfig oneClickMigrationConfig;
        private FlywayValidationConfig flywayValidationConfig;
        private TimerPathFixConfig timerPathFixConfig;
        private SysConfigModifyConfig sysConfigModifyConfig;
        private SqlExecutionConfig sqlExecutionConfig;

        public AppConfig() {
            this.migrationConfig = new MigrationConfig();
            this.oneClickMigrationConfig = new OneClickMigrationConfig();
            this.flywayValidationConfig = new FlywayValidationConfig();
            this.timerPathFixConfig = new TimerPathFixConfig();
            this.sysConfigModifyConfig = new SysConfigModifyConfig();
            this.sqlExecutionConfig = new SqlExecutionConfig();
        }
    }

    @Data
    public static class NamedConfig {
        private String name;
        private String type; // "DATABASE_CONFIG" - 只用于数据库配置
        private DatabaseConfig sourceDatabase;
        private DatabaseConfig targetDatabase;
        private java.time.LocalDateTime createdTime;
        private java.time.LocalDateTime lastModified;

        public NamedConfig() {
            this.sourceDatabase = new DatabaseConfig();
            this.targetDatabase = new DatabaseConfig();
            this.createdTime = java.time.LocalDateTime.now();
            this.lastModified = java.time.LocalDateTime.now();
        }

        public NamedConfig(String name, String type) {
            this();
            this.name = name;
            this.type = type;
        }
    }
    
    /**
     * 迁移配置
     */
    @Data
    public static class MigrationConfig {
        // 选择的数据库配置名称
        private String selectedDatabaseConfig;

        private String exportPath = System.getProperty("user.home") + File.separator + "Desktop" + File.separator + "data-export";
        private String importPath = "";
        private String exportSourceSchema = "";
        private String exportTargetSchema = "";
        private String importTargetSchema = "";
        private DatabaseConfig.DatabaseType targetDbType = DatabaseConfig.DatabaseType.MYSQL;
        private boolean tableMerging = true;
        private boolean encrypt = false;
        private boolean decrypt = false;
    }
    
    /**
     * 一键迁移到精简配置
     */
    @Data
    public static class OneClickMigrationConfig {
        // 选择的数据库配置名称
        private String selectedDatabaseConfig;

        private String schemaPrefixSql = "select code from systemx.tenant_info";
        private List<SchemaMapping> schemaMappings = new ArrayList<>();
        private String exportPath = System.getProperty("user.home") + File.separator + "Desktop" + File.separator + "oneclick-export";
        private boolean tableMerging = true;
        private boolean encrypt = false;

        public OneClickMigrationConfig() {
            // 添加默认映射：modelx->scenex，twinx->scenex
            schemaMappings.add(new SchemaMapping("modelx", "scenex"));
            schemaMappings.add(new SchemaMapping("twinx", "scenex"));
        }
    }

    /**
     * Flyway校验修复配置
     */
    @Data
    public static class FlywayValidationConfig {
        // 选择的数据库配置名称
        private String selectedDatabaseConfig;

        // Schema配置
        private String schemaInput = "";
        private boolean prefixMatch = false;
        private boolean suffixMatch = false;

        // 强制覆盖配置
        private boolean forceOverride = false;
        private String sourceSchema = "";

        // 数据映射配置
        private List<DataMappingConfig> dataMappings = new ArrayList<>();

        // 删除版本配置
        private List<String> deleteVersions = new ArrayList<>();

        // 添加版本配置
        private List<AddVersionConfig> addVersions = new ArrayList<>();

        public FlywayValidationConfig() {
            // 添加一个默认的数据映射
            dataMappings.add(new DataMappingConfig("", "checksum", ""));
        }
    }

    /**
     * 数据映射配置
     */
    @Data
    public static class DataMappingConfig {
        private String version = "";
        private String fieldName = "checksum";
        private String fieldValue = "";

        public DataMappingConfig() {}

        public DataMappingConfig(String version, String fieldName, String fieldValue) {
            this.version = version;
            this.fieldName = fieldName;
            this.fieldValue = fieldValue;
        }
    }

    /**
     * 添加版本配置
     */
    @Data
    public static class AddVersionConfig {
        private String version = "";
        private String description = "";
        private String script = "";
        private String checksum = "";

        public AddVersionConfig() {}

        public AddVersionConfig(String version, String description, String script, String checksum) {
            this.version = version;
            this.description = description;
            this.script = script;
            this.checksum = checksum;
        }
    }

    /**
     * Schema映射配置
     */
    @Data
    public static class SchemaMapping {
        private String sourceSuffix = "";
        private String targetSuffix = "";
        
        public SchemaMapping() {}
        
        public SchemaMapping(String sourceSuffix, String targetSuffix) {
            this.sourceSuffix = sourceSuffix;
            this.targetSuffix = targetSuffix;
        }
    }
    
    /**
     * 保存配置到文件
     */
    public static void saveConfig(AppConfig config) {
        try {
            // 确保配置目录存在
            Files.createDirectories(Paths.get(CONFIG_DIR));
            
            // 保存配置
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(new File(CONFIG_FILE), config);
            log.info("配置已保存到: {}", CONFIG_FILE);
            
        } catch (IOException e) {
            log.error("保存配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 从文件加载配置
     */
    public static AppConfig loadConfig() {
        try {
            File configFile = new File(CONFIG_FILE);
            if (configFile.exists()) {
                AppConfig config = objectMapper.readValue(configFile, AppConfig.class);
                log.info("配置已从文件加载: {}", CONFIG_FILE);
                return config;
            } else {
                log.info("配置文件不存在，使用默认配置");
                return new AppConfig();
            }
        } catch (IOException e) {
            log.error("加载配置失败，使用默认配置: {}", e.getMessage());
            return new AppConfig();
        }
    }
    
    /**
     * 检查配置文件是否存在
     */
    public static boolean configExists() {
        return new File(CONFIG_FILE).exists();
    }
    
    /**
     * 删除配置文件
     */
    public static boolean deleteConfig() {
        try {
            File configFile = new File(CONFIG_FILE);
            if (configFile.exists()) {
                boolean deleted = configFile.delete();
                if (deleted) {
                    log.info("配置文件已删除: {}", CONFIG_FILE);
                }
                return deleted;
            }
            return true;
        } catch (Exception e) {
            log.error("删除配置文件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取配置文件路径
     */
    public static String getConfigFilePath() {
        return CONFIG_FILE;
    }

    /**
     * 保存数据库配置
     */
    public static void saveNamedConfig(String name, String type, DatabaseConfig sourceDb, DatabaseConfig targetDb) {
        AppConfig appConfig = loadConfig();

        // 获取现有配置或创建新配置
        NamedConfig namedConfig = appConfig.getNamedConfigs().get(name);
        if (namedConfig == null) {
            namedConfig = new NamedConfig(name, type);
        }

        // 更新配置
        namedConfig.setName(name);
        namedConfig.setType(type);
        namedConfig.setSourceDatabase(sourceDb);
        namedConfig.setTargetDatabase(targetDb);
        namedConfig.setLastModified(java.time.LocalDateTime.now());

        appConfig.getNamedConfigs().put(name, namedConfig);
        saveConfig(appConfig);

        log.info("数据库配置已保存: name={}, type={}", name, type);
    }

    /**
     * 获取所有命名配置
     */
    public static java.util.Map<String, NamedConfig> getAllNamedConfigs() {
        AppConfig appConfig = loadConfig();
        return appConfig.getNamedConfigs();
    }

    /**
     * 获取指定名称的配置
     */
    public static NamedConfig getNamedConfig(String name) {
        AppConfig appConfig = loadConfig();
        return appConfig.getNamedConfigs().get(name);
    }

    /**
     * 删除命名配置
     */
    public static boolean deleteNamedConfig(String name) {
        AppConfig appConfig = loadConfig();
        NamedConfig removed = appConfig.getNamedConfigs().remove(name);
        if (removed != null) {
            saveConfig(appConfig);
            log.info("命名配置已删除: {}", name);
            return true;
        }
        return false;
    }

    /**
     * 定时任务路径修复配置
     */
    @Data
    public static class TimerPathFixConfig {
        // 选择的数据库配置名称
        private String selectedDatabaseConfig;

        // Schema配置
        private String schemaInput = "";
        private boolean prefixMatch = false;
        private boolean suffixMatch = false;

        // 包名映射配置
        private List<PackageMappingConfig> packageMappings = new ArrayList<>();

        public TimerPathFixConfig() {
            // 添加一个默认的包名映射
            packageMappings.add(new PackageMappingConfig("com.uino.x.system.timer.impl.function", "com.uino.x.pedestal.timer.impl.runner"));
        }
    }

    /**
     * 包名映射配置
     */
    @Data
    public static class PackageMappingConfig {
        private String sourcePackage;
        private String targetPackage;

        public PackageMappingConfig() {}

        public PackageMappingConfig(String sourcePackage, String targetPackage) {
            this.sourcePackage = sourcePackage;
            this.targetPackage = targetPackage;
        }
    }

    /**
     * 系统配置修改配置
     */
    @Data
    public static class SysConfigModifyConfig {
        // 选择的数据库配置名称
        private String selectedDatabaseConfig;

        // Schema配置
        private String schemaInput = "";
        private boolean prefixMatch = false;
        private boolean suffixMatch = false;

        // 数据映射配置
        private List<SysConfigMappingConfig> dataMappings = new ArrayList<>();

        public SysConfigModifyConfig() {
            // 添加一个默认的数据映射
            dataMappings.add(new SysConfigMappingConfig("", "name", ""));
        }
    }

    /**
     * 系统配置数据映射配置
     */
    @Data
    public static class SysConfigMappingConfig {
        private String code = "";
        private String fieldName = "";
        private String fieldValue = "";

        public SysConfigMappingConfig() {}

        public SysConfigMappingConfig(String code, String fieldName, String fieldValue) {
            this.code = code;
            this.fieldName = fieldName;
            this.fieldValue = fieldValue;
        }
    }

    /**
     * SQL执行配置
     */
    @Data
    public static class SqlExecutionConfig {
        // 选择的数据库配置名称
        private String selectedDatabaseConfig;

        // SQL内容
        private String sqlContent = "-- 请在此输入SQL语句\nSELECT * FROM sys_config LIMIT 10;";

        // Schema配置
        private String schemaInput = "";
        private boolean prefixMatch = false;
        private boolean suffixMatch = false;

        // 排除Schema列表
        private List<String> excludeSchemas = new ArrayList<>();

        public SqlExecutionConfig() {}

        public SqlExecutionConfig(String selectedDatabaseConfig, String sqlContent) {
            this.selectedDatabaseConfig = selectedDatabaseConfig;
            this.sqlContent = sqlContent;
        }
    }
}
