2025-08-22 09:59:26.362 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:26.370 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-22 09:59:26.625 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:26.630 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 09:59:26.782 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:26.783 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 09:59:26.850 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-22 09:59:26.869 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - Created 11 default transformation strategies
2025-08-22 09:59:26.920 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 11 transformation strategies in engine [MySQL-to-达梦]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, DataTypeTransformationStrategy, GroupConcatTransformationStrategy, AlterTableCommentTransformationStrategy, BacktickQuoteTransformationStrategy]
2025-08-22 09:59:26.921 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 11 strategies
2025-08-22 09:59:26.921 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-22 09:59:26.921 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-22 09:59:26.945 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.003 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 09:59:27.068 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.071 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 09:59:27.072 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 09:59:27.084 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.086 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 09:59:27.088 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.090 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 3 个配置
2025-08-22 09:59:27.093 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.094 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 09:59:27.097 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.098 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 09:59:27.104 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 31dm-40m
2025-08-22 09:59:27.106 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.108 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=SYSDBA
2025-08-22 09:59:27.115 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=sys
2025-08-22 09:59:27.117 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已应用数据库配置: 31dm-40m
2025-08-22 09:59:27.117 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 31dm-40m
2025-08-22 09:59:27.119 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.120 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=SYSDBA
2025-08-22 09:59:27.121 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=sys
2025-08-22 09:59:27.121 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已应用数据库配置: 31dm-40m
2025-08-22 09:59:27.125 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.127 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=sys
2025-08-22 09:59:27.128 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=sys
2025-08-22 09:59:27.129 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已应用数据库配置: 3.40-3307
2025-08-22 09:59:27.131 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.132 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=sys
2025-08-22 09:59:27.133 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=sys
2025-08-22 09:59:27.133 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已应用数据库配置: 3.40-3307
2025-08-22 09:59:27.153 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.157 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.210 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.210 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已选择数据库配置: 3.40-3307
2025-08-22 09:59:27.213 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.213 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已选择数据库配置: 3.40-3307
2025-08-22 09:59:27.216 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.218 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 09:59:27.224 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 09:59:30.743 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-22 10:01:50.736 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 40m-31dm
2025-08-22 10:01:50.738 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:01:50.739 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=sys
2025-08-22 10:01:50.742 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=SYSDBA
2025-08-22 10:01:50.742 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已应用数据库配置: 40m-31dm
2025-08-22 10:02:02.154 [SwingWorker-pool-2-thread-1] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:02:33.446 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TableSelectionDialog - 用户选择了 2 个表和视图进行导出
2025-08-22 10:02:33.447 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 用户选择了 2 个表和 0 个视图
2025-08-22 10:02:39.903 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:02:39.903 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.ui.MigrationPanel - 使用源数据库配置: host=***********, port=3307, database=scenex, username=root
2025-08-22 10:02:39.988 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 开始导出Schema: scenex (不转换Schema) 到路径: C:\Users\<USER>\Desktop\data-export
2025-08-22 10:02:39.989 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 导出选中的表/视图: [twin_legend, twin_menu]
2025-08-22 10:02:40.271 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 导出表: twin_legend
2025-08-22 10:02:40.645 [SwingWorker-pool-2-thread-2] DEBUG com.uino.x.migration.service.DatabaseService - 表 twin_legend DROP SQL已保存到: C:\Users\<USER>\Desktop\data-export\twin_legend_drop.sql
2025-08-22 10:02:40.646 [SwingWorker-pool-2-thread-2] DEBUG com.uino.x.migration.service.DatabaseService - 表 twin_legend 结构SQL已保存到: C:\Users\<USER>\Desktop\data-export\twin_legend_create.sql
2025-08-22 10:02:40.647 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 表 twin_legend 导出完成（目标格式：DM）
2025-08-22 10:02:40.647 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 导出表: twin_menu
2025-08-22 10:02:40.984 [SwingWorker-pool-2-thread-2] DEBUG com.uino.x.migration.service.DatabaseService - 表 twin_menu DROP SQL已保存到: C:\Users\<USER>\Desktop\data-export\twin_menu_drop.sql
2025-08-22 10:02:40.985 [SwingWorker-pool-2-thread-2] DEBUG com.uino.x.migration.service.DatabaseService - 表 twin_menu 结构SQL已保存到: C:\Users\<USER>\Desktop\data-export\twin_menu_create.sql
2025-08-22 10:02:40.986 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 表 twin_menu 导出完成（目标格式：DM）
2025-08-22 10:02:40.986 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 选中的 2 个表/视图导出完成
2025-08-22 10:02:40.986 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - Schema导出完成: C:\Users\<USER>\Desktop\data-export
2025-08-22 10:04:35.347 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:04:35.348 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:04:37.514 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation statistics: 
SqlTransformationEngine [MySQL-to-达梦] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 11 registered
  Memory: Estimated Size=0 bytes
2025-08-22 10:04:37.614 [AWT-EventQueue-0] WARN  c.u.x.c.s.t.s.MergeIntoTransformationStrategy - Failed to retrieve column metadata for table: twin_class -> Cannot invoke "org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(java.lang.Class)" because the return value of "com.uino.x.common.tool.spring.SpringIocUtils.defaultBeanFactory()" is null
2025-08-22 10:04:37.695 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [REPLACE INTO `twin_class`(`id`, `name`, `code`, `group_id`, `level`, `data_type`, `custom_color`, `bubble_info_id`, `things_model_id`, `things_model_uuid`, `status`, `form`, `structure`, `pure`, `create_user`) VALUES (1486428435602665473, 'XXV菜单', 'MENU', 1476825472376508418, 'OTHER', 'FORM', NULL, NULL, NULL, NULL, 0, '{\"list\":[{\"type\":\"input\",\"label\":\"孪生体编码\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"UniqueCode\",\"key\":\"input_1635917827146\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\"}]},{\"type\":\"input\",\"label\":\"视角\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"view\",\"key\":\"input_1676605829257\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\",\"type\":\"string\"}]}],\"config\":{\"layout\":\"horizontal\",\"labelCol\":{\"xs\":4,\"sm\":4,\"md\":4,\"lg\":4,\"xl\":4,\"xxl\":4},\"labelWidth\":100,\"labelLayout\":\"flex\",\"wrapperCol\":{\"xs\":18,\"sm\":18,\"md\":18,\"lg\":18,\"xl\":18,\"xxl\":18},\"hideRequiredMark\":false,\"customStyle\":\"\"}}', '{\"table\":\"twin_menu\",\"column\":[{\"code\":\"9d3b4c28dfea84b76922211e7b75963f\",\"comment\":\"唯一标识\",\"name\":\"uuid\",\"required\":\"not null\",\"type\":\"bigint primary key\",\"unique\":true},{\"code\":\"input_1635917827146\",\"comment\":\"孪生体编码\",\"length\":255,\"name\":\"UniqueCode\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"input_1676605829257\",\"comment\":\"视角\",\"length\":255,\"name\":\"view\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1ee9d83c592de78123b4dcdcfa0c1a80\",\"comment\":\"创建时间\",\"defaultValue\":\"current_timestamp\",\"name\":\"create_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"fa1506f5f75d60eb2bc62ce19ad17121\",\"comment\":\"创建人\",\"length\":127,\"name\":\"create_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1a8422f18b26369c37923b24b9a20c7d\",\"comment\":\"更新时间\",\"defaultValue\":\"current_timestamp on update current_timestamp\",\"name\":\"update_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"2a429205c9d6b3600745529d0e978eb8\",\"comment\":\"更新人\",\"length\":127,\"name\":\"update_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false}],\"@version\":1,\"@session\":\"1626516592439603201\"}', 0, 1557912667941769217);]
Result:   [MERGE INTO "twin_class" t USING (SELECT 1486428435602665473 AS id, 'XXV菜单' AS name, 'MENU' AS code, 1476825472376508418 AS group_id, 'OTHER' AS level, 'FORM' AS data_type, NULL AS custom_color, NULL AS bubble_info_id, NULL AS things_model_id, NULL AS things_model_uuid, 0 AS status, '{\"list\":[{\"type\":\"input\",\"label\":\"孪生体编码\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"UniqueCode\",\"key\":\"input_1635917827146\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\"}]},{\"type\":\"input\",\"label\":\"视角\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"view\",\"key\":\"input_1676605829257\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\",\"type\":\"string\"}]}],\"config\":{\"layout\":\"horizontal\",\"labelCol\":{\"xs\":4,\"sm\":4,\"md\":4,\"lg\":4,\"xl\":4,\"xxl\":4},\"labelWidth\":100,\"labelLayout\":\"flex\",\"wrapperCol\":{\"xs\":18,\"sm\":18,\"md\":18,\"lg\":18,\"xl\":18,\"xxl\":18},\"hideRequiredMark\":false,\"customStyle\":\"\"}}' AS form, '{\"table\":\"twin_menu\",\"column\":[{\"code\":\"9d3b4c28dfea84b76922211e7b75963f\",\"comment\":\"唯一标识\",\"name\":\"uuid\",\"required\":\"not null\",\"type\":\"bigint primary key\",\"unique\":true},{\"code\":\"input_1635917827146\",\"comment\":\"孪生体编码\",\"length\":255,\"name\":\"UniqueCode\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"input_1676605829257\",\"comment\":\"视角\",\"length\":255,\"name\":\"view\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1ee9d83c592de78123b4dcdcfa0c1a80\",\"comment\":\"创建时间\",\"defaultValue\":\"current_timestamp\",\"name\":\"create_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"fa1506f5f75d60eb2bc62ce19ad17121\",\"comment\":\"创建人\",\"length\":127,\"name\":\"create_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1a8422f18b26369c37923b24b9a20c7d\",\"comment\":\"更新时间\",\"defaultValue\":\"current_timestamp on update current_timestamp\",\"name\":\"update_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"2a429205c9d6b3600745529d0e978eb8\",\"comment\":\"更新人\",\"length\":127,\"name\":\"update_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false}],\"@version\":1,\"@session\":\"1626516592439603201\"}' AS structure, 0 AS pure, 1557912667941769217 AS create_user FROM DUAL) s ON (t.id = s.id) WHEN MATCHED THEN UPDATE SET t.name = s.name, t.code = s.code, t.group_id = s.group_id, t.level = s.level, t.data_type = s.data_type, t.custom_color = s.custom_color, t.bubble_info_id = s.bubble_info_id, t.things_model_id = s.things_model_id, t.things_model_uuid = s.things_model_uuid, t.status = s.status, t.form = s.form, t.structure = s.structure, t.pure = s.pure, t.create_user = s.create_user WHEN NOT MATCHED THEN INSERT (id, name, code, group_id, level, data_type, custom_color, bubble_info_id, things_model_id, things_model_uuid, status, form, structure, pure, create_user) VALUES (s.id, s.name, s.code, s.group_id, s.level, s.data_type, s.custom_color, s.bubble_info_id, s.things_model_id, s.things_model_uuid, s.status, s.form, s.structure, s.pure, s.create_user);]
2025-08-22 10:04:37.697 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 2749, 转换后SQL长度: 3558
2025-08-22 10:06:22.281 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [ALTER TABLE `twin_class_model_mapping`
    ADD COLUMN `bubble_info_id` bigint(0) NULL COMMENT '气泡ID';]
Result:   [ALTER TABLE "twin_class_model_mapping" ADD COLUMN "bubble_info_id" bigint NULL;;
COMMENT ON COLUMN "twin_class_model_mapping"."bubble_info_id" IS '气泡ID';]
2025-08-22 10:06:22.282 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 101, 转换后SQL长度: 153
2025-08-22 10:06:34.400 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [ALTER TABLE `twin_class_model_mapping`
    ADD COLUMN `bubble_info_name` varchar(255) NULL COMMENT '气泡名称';]
Result:   [ALTER TABLE "twin_class_model_mapping" ADD COLUMN "bubble_info_name" varchar(255) NULL;;
COMMENT ON COLUMN "twin_class_model_mapping"."bubble_info_name" IS '气泡名称';]
2025-08-22 10:06:34.404 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 106, 转换后SQL长度: 163
2025-08-22 10:06:44.456 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [ALTER TABLE `twin_permission`
    MODIFY COLUMN `read_column` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '可读字段',
    MODIFY COLUMN `add_column` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '可增加字段',
    MODIFY COLUMN `edit_column` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '可编辑字段';]
Result:   [ALTER TABLE "twin_permission" MODIFY COLUMN "read_column" text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, MODIFY COLUMN "add_column" text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '可增加字段', MODIFY COLUMN "edit_column" text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '可编辑字段';]
2025-08-22 10:06:44.457 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 351, 转换后SQL长度: 324
2025-08-22 10:07:37.838 [AWT-EventQueue-0] WARN  c.u.x.c.s.t.s.MergeIntoTransformationStrategy - Failed to retrieve column metadata for table: twin_class -> Cannot invoke "org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(java.lang.Class)" because the return value of "com.uino.x.common.tool.spring.SpringIocUtils.defaultBeanFactory()" is null
2025-08-22 10:07:37.850 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [REPLACE INTO `twin_class`(`id`, `name`, `code`, `group_id`, `level`, `data_type`, `custom_color`, `bubble_info_id`, `things_model_id`, `things_model_uuid`, `status`, `form`, `structure`, `pure`, `create_time`, `create_user`, `update_time`, `update_user`) VALUES (1947120146044686337, 'XXV图例', 'LEGEND', 1476825472376508418, 'OTHER', 'FORM', NULL, NULL, NULL, NULL, 0, '{\"list\":[{\"type\":\"input\",\"label\":\"孪生体编码\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"UniqueCode\",\"key\":\"input_1635917827146\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\"}]}],\"config\":{\"layout\":\"horizontal\",\"labelCol\":{\"xs\":4,\"sm\":4,\"md\":4,\"lg\":4,\"xl\":4,\"xxl\":4},\"labelWidth\":100,\"labelLayout\":\"flex\",\"wrapperCol\":{\"xs\":18,\"sm\":18,\"md\":18,\"lg\":18,\"xl\":18,\"xxl\":18},\"hideRequiredMark\":false,\"customStyle\":\"\"}}', '{\"table\":\"twin_legend\",\"column\":[{\"code\":\"9d3b4c28dfea84b76922211e7b75963f\",\"comment\":\"唯一标识\",\"name\":\"uuid\",\"required\":\"not null\",\"type\":\"bigint primary key\",\"unique\":true},{\"code\":\"input_1635917827146\",\"comment\":\"孪生体编码\",\"length\":255,\"name\":\"UniqueCode\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1ee9d83c592de78123b4dcdcfa0c1a80\",\"comment\":\"创建时间\",\"defaultValue\":\"current_timestamp\",\"name\":\"create_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"fa1506f5f75d60eb2bc62ce19ad17121\",\"comment\":\"创建人\",\"length\":127,\"name\":\"create_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1a8422f18b26369c37923b24b9a20c7d\",\"comment\":\"更新时间\",\"defaultValue\":\"current_timestamp on update current_timestamp\",\"name\":\"update_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"2a429205c9d6b3600745529d0e978eb8\",\"comment\":\"更新人\",\"length\":127,\"name\":\"update_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false}],\"@version\":1,\"@session\":\"10.203.3.61:8091:126772690604691175\"}', 0, '2025-07-21 10:23:13', 1, '2025-07-21 10:23:32', NULL);]
Result:   [MERGE INTO "twin_class" t USING (SELECT 1947120146044686337 AS id, 'XXV图例' AS name, 'LEGEND' AS code, 1476825472376508418 AS group_id, 'OTHER' AS level, 'FORM' AS data_type, NULL AS custom_color, NULL AS bubble_info_id, NULL AS things_model_id, NULL AS things_model_uuid, 0 AS status, '{\"list\":[{\"type\":\"input\",\"label\":\"孪生体编码\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"UniqueCode\",\"key\":\"input_1635917827146\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\"}]}],\"config\":{\"layout\":\"horizontal\",\"labelCol\":{\"xs\":4,\"sm\":4,\"md\":4,\"lg\":4,\"xl\":4,\"xxl\":4},\"labelWidth\":100,\"labelLayout\":\"flex\",\"wrapperCol\":{\"xs\":18,\"sm\":18,\"md\":18,\"lg\":18,\"xl\":18,\"xxl\":18},\"hideRequiredMark\":false,\"customStyle\":\"\"}}' AS form, '{\"table\":\"twin_legend\",\"column\":[{\"code\":\"9d3b4c28dfea84b76922211e7b75963f\",\"comment\":\"唯一标识\",\"name\":\"uuid\",\"required\":\"not null\",\"type\":\"bigint primary key\",\"unique\":true},{\"code\":\"input_1635917827146\",\"comment\":\"孪生体编码\",\"length\":255,\"name\":\"UniqueCode\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1ee9d83c592de78123b4dcdcfa0c1a80\",\"comment\":\"创建时间\",\"defaultValue\":\"current_timestamp\",\"name\":\"create_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"fa1506f5f75d60eb2bc62ce19ad17121\",\"comment\":\"创建人\",\"length\":127,\"name\":\"create_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1a8422f18b26369c37923b24b9a20c7d\",\"comment\":\"更新时间\",\"defaultValue\":\"current_timestamp on update current_timestamp\",\"name\":\"update_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"2a429205c9d6b3600745529d0e978eb8\",\"comment\":\"更新人\",\"length\":127,\"name\":\"update_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false}],\"@version\":1,\"@session\":\"10.203.3.61:8091:126772690604691175\"}' AS structure, 0 AS pure, '2025-07-21 10:23:13' AS create_time, 1 AS create_user, '2025-07-21 10:23:32' AS update_time, NULL AS update_user FROM DUAL) s ON (t.id = s.id) WHEN MATCHED THEN UPDATE SET t.name = s.name, t.code = s.code, t.group_id = s.group_id, t.level = s.level, t.data_type = s.data_type, t.custom_color = s.custom_color, t.bubble_info_id = s.bubble_info_id, t.things_model_id = s.things_model_id, t.things_model_uuid = s.things_model_uuid, t.status = s.status, t.form = s.form, t.structure = s.structure, t.pure = s.pure, t.create_time = s.create_time, t.create_user = s.create_user, t.update_time = s.update_time, t.update_user = s.update_user WHEN NOT MATCHED THEN INSERT (id, name, code, group_id, level, data_type, custom_color, bubble_info_id, things_model_id, things_model_uuid, status, form, structure, pure, create_time, create_user, update_time, update_user) VALUES (s.id, s.name, s.code, s.group_id, s.level, s.data_type, s.custom_color, s.bubble_info_id, s.things_model_id, s.things_model_uuid, s.status, s.form, s.structure, s.pure, s.create_time, s.create_user, s.update_time, s.update_user);]
2025-08-22 10:07:37.851 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 2296, 转换后SQL长度: 3282
2025-08-22 10:17:07.672 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [ALTER TABLE `twin_permission`
    MODIFY COLUMN `read_column` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '可读字段',]
Result:   [ALTER TABLE "twin_permission" MODIFY COLUMN "read_column" text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,;
COMMENT ON COLUMN "twin_permission"."read_column" IS '可读字段';]
2025-08-22 10:17:07.673 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 136, 转换后SQL长度: 179
2025-08-22 10:35:11.604 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:39:56.570 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:09.125 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.140 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-22 10:40:09.341 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.348 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 10:40:09.481 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.483 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:40:09.543 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-22 10:40:09.556 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - Created 11 default transformation strategies
2025-08-22 10:40:09.597 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 11 transformation strategies in engine [MySQL-to-达梦]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, DataTypeTransformationStrategy, GroupConcatTransformationStrategy, AlterTableCommentTransformationStrategy, BacktickQuoteTransformationStrategy]
2025-08-22 10:40:09.597 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 11 strategies
2025-08-22 10:40:09.598 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-22 10:40:09.598 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-22 10:40:09.622 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.626 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:40:09.675 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.677 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:40:09.678 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:40:09.678 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.679 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:40:09.680 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.680 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 0 个配置
2025-08-22 10:40:09.681 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.682 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:40:09.683 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:09.684 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:40:09.708 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:40:10.626 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-22 10:40:21.730 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:40:28.043 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:40:28.044 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:30.836 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 已打开配置文件目录
2025-08-22 10:40:43.730 [AWT-EventQueue-0] ERROR com.uino.x.migration.config.ConfigManager - 加载配置失败，使用默认配置: No content to map due to end-of-input
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1]
2025-08-22 10:40:43.783 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:43.800 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:40:43.834 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:43.836 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:43.836 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-22 10:40:43.836 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:40:43.836 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:40:43.837 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:43.837 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:40:43.837 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:43.838 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 0 个配置
2025-08-22 10:40:43.838 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:43.838 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:40:43.839 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:40:43.839 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:40:43.850 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:40:43.850 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-22 10:40:45.324 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - Flyway校验修复配置已保存
2025-08-22 10:40:47.714 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:40:50.018 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:41:16.386 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.388 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.397 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:41:16.399 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.399 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.400 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-22 10:41:16.400 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:41:16.400 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:41:16.401 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.401 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:41:16.402 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.402 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 0 个配置
2025-08-22 10:41:16.403 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.403 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:41:16.404 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:16.404 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:41:16.416 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:41:16.416 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-22 10:41:17.418 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - Flyway校验修复配置已保存
2025-08-22 10:41:25.033 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.034 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.036 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:41:25.037 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.046 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.046 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-22 10:41:25.047 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:41:25.047 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:41:25.047 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.047 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:41:25.048 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.048 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 0 个配置
2025-08-22 10:41:25.048 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.049 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:41:25.049 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:41:25.049 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:41:25.058 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:41:25.058 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-22 10:41:26.226 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - Flyway校验修复配置已保存
2025-08-22 10:43:02.301 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.302 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.308 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.308 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 数据库配置已保存: name=40mysql-31达梦, type=DATABASE_CONFIG
2025-08-22 10:43:02.320 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.321 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 10:43:02.323 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.324 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=sys
2025-08-22 10:43:02.324 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=SYSDBA
2025-08-22 10:43:02.324 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已自动加载数据库配置: 40mysql-31达梦
2025-08-22 10:43:02.325 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.326 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.332 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:43:02.333 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.334 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.335 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-22 10:43:02.335 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:43:02.335 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:43:02.336 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.337 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:43:02.337 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.338 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 1 个配置
2025-08-22 10:43:02.339 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.340 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:43:02.341 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:02.341 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:43:02.354 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:43:02.354 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-22 10:43:03.244 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 数据库配置已保存: name=40mysql-31达梦
2025-08-22 10:43:54.507 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.508 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.509 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.509 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 数据库配置已保存: name=31达梦-40mysql, type=DATABASE_CONFIG
2025-08-22 10:43:54.521 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.521 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 10:43:54.522 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.523 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=SYSDBA
2025-08-22 10:43:54.523 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=sys
2025-08-22 10:43:54.523 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已自动加载数据库配置: 31达梦-40mysql
2025-08-22 10:43:54.525 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.526 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.528 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:43:54.529 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.529 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.529 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-22 10:43:54.530 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:43:54.530 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:43:54.530 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.531 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:43:54.531 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.531 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 10:43:54.532 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.533 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:43:54.534 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:43:54.534 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:43:54.543 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:43:54.543 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-22 10:43:55.356 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 数据库配置已保存: name=31达梦-40mysql
2025-08-22 10:44:00.906 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.115 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.117 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.128 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:44:04.129 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.129 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.130 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-22 10:44:04.130 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:44:04.130 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:44:04.131 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.131 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:44:04.132 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.132 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 10:44:04.132 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.133 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:44:04.134 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.134 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:44:04.143 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.143 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:04.149 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:44:04.149 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-22 10:44:05.322 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - Flyway校验修复配置已保存
2025-08-22 10:44:23.547 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 31达梦-40mysql
2025-08-22 10:44:23.548 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:23.548 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=SYSDBA
2025-08-22 10:44:23.549 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=sys
2025-08-22 10:44:23.549 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已应用数据库配置: 31达梦-40mysql
2025-08-22 10:44:36.987 [SwingWorker-pool-2-thread-5] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:39.939 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TableSelectionDialog - 用户选择了 1 个表和视图进行导出
2025-08-22 10:44:39.939 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 用户选择了 1 个表和 0 个视图
2025-08-22 10:44:44.296 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:44:44.296 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.ui.MigrationPanel - 使用源数据库配置: host=***********, port=5236, database=scenex, username=SYSDBA
2025-08-22 10:44:44.343 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.service.DatabaseService - 开始导出Schema: scenex (不转换Schema) 到路径: C:\Users\<USER>\Desktop\data-export
2025-08-22 10:44:44.344 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.service.DatabaseService - 导出选中的表/视图: [flyway_schema_history]
2025-08-22 10:44:44.414 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.service.DatabaseService - 导出表: flyway_schema_history
2025-08-22 10:44:44.592 [SwingWorker-pool-2-thread-6] DEBUG com.uino.x.migration.service.DatabaseService - 表 flyway_schema_history DROP SQL已保存到: C:\Users\<USER>\Desktop\data-export\flyway_schema_history_drop.sql
2025-08-22 10:44:44.593 [SwingWorker-pool-2-thread-6] DEBUG com.uino.x.migration.service.DatabaseService - 表 flyway_schema_history 结构SQL已保存到: C:\Users\<USER>\Desktop\data-export\flyway_schema_history_create.sql
2025-08-22 10:44:44.597 [SwingWorker-pool-2-thread-6] DEBUG com.uino.x.migration.service.DatabaseService - 表 flyway_schema_history 数据SQL已保存到: C:\Users\<USER>\Desktop\data-export\flyway_schema_history_data.sql
2025-08-22 10:44:44.597 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.service.DatabaseService - 表 flyway_schema_history 导出完成（目标格式：DM）
2025-08-22 10:44:44.597 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.service.DatabaseService - 选中的 1 个表/视图导出完成
2025-08-22 10:44:44.598 [SwingWorker-pool-2-thread-6] INFO  com.uino.x.migration.service.DatabaseService - Schema导出完成: C:\Users\<USER>\Desktop\data-export
2025-08-22 10:46:19.565 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:19.568 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-22 10:46:19.747 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:19.751 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 10:46:19.867 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:19.868 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:46:19.919 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-22 10:46:19.933 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - Created 11 default transformation strategies
2025-08-22 10:46:19.957 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 11 transformation strategies in engine [MySQL-to-达梦]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, DataTypeTransformationStrategy, GroupConcatTransformationStrategy, AlterTableCommentTransformationStrategy, BacktickQuoteTransformationStrategy]
2025-08-22 10:46:19.957 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 11 strategies
2025-08-22 10:46:19.958 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-22 10:46:19.958 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-22 10:46:19.976 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.008 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 10:46:20.039 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.041 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 10:46:20.042 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 10:46:20.043 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.044 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 10:46:20.045 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.046 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 10:46:20.050 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.051 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 10:46:20.052 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.053 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 10:46:20.065 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.067 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:20.077 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 10:46:20.499 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-22 10:46:31.278 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 10:46:31.280 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始执行Flyway校验修复，Schema输入: scenex, 前缀匹配: false, 后缀匹配: true
2025-08-22 10:46:31.549 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 找到 5 个匹配的Schema: [aaaaa_scenex, bzmmx_scenex, project_scenex, scenex, xqjii_scenex]
2025-08-22 10:46:31.549 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始处理Schema: aaaaa_scenex
2025-08-22 10:46:31.549 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 没有数据映射、删除版本或添加版本需要处理
2025-08-22 10:46:31.607 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema aaaaa_scenex 中没有找到类型为DELETE的记录
2025-08-22 10:46:31.607 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始重新排序Schema aaaaa_scenex 的flyway_schema_history表
2025-08-22 10:46:31.608 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 查询schema [aaaaa_scenex] 的flyway_schema_history表，SQL: SELECT installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success FROM aaaaa_scenex.flyway_schema_history ORDER BY installed_rank
2025-08-22 10:46:31.615 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.615 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.616 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Type=SQL
2025-08-22 10:46:31.616 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Type=SQL
2025-08-22 10:46:31.616 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Type=SQL
2025-08-22 10:46:31.616 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Type=SQL
2025-08-22 10:46:31.616 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Type=SQL
2025-08-22 10:46:31.616 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Type=SQL
2025-08-22 10:46:31.617 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Type=SQL
2025-08-22 10:46:31.617 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Type=SQL
2025-08-22 10:46:31.617 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Type=SQL
2025-08-22 10:46:31.617 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 12: Rank=12, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Type=SQL
2025-08-22 10:46:31.617 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 13: Rank=13, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Type=SQL
2025-08-22 10:46:31.618 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 14: Rank=14, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Type=SQL
2025-08-22 10:46:31.618 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Type=SQL
2025-08-22 10:46:31.618 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 16: Rank=16, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Type=SQL
2025-08-22 10:46:31.618 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 17: Rank=17, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Type=SQL
2025-08-22 10:46:31.618 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 18: Rank=18, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Type=SQL
2025-08-22 10:46:31.618 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 从schema [aaaaa_scenex] 成功查询到 18 条flyway_schema_history记录
2025-08-22 10:46:31.618 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema aaaaa_scenex 中共有 18 条记录需要重新排序
2025-08-22 10:46:31.623 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 删除了Schema aaaaa_scenex 中的 18 条flyway_schema_history记录
2025-08-22 10:46:31.624 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 准备批量插入 18 条记录，SQL: INSERT INTO aaaaa_scenex.flyway_schema_history (installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-08-22 10:46:31.633 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=746558939
2025-08-22 10:46:31.633 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=734435211
2025-08-22 10:46:31.633 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Checksum=-85342651
2025-08-22 10:46:31.634 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Checksum=1766918736
2025-08-22 10:46:31.634 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Checksum=-164998370
2025-08-22 10:46:31.634 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Checksum=257700874
2025-08-22 10:46:31.634 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Checksum=-1713726568
2025-08-22 10:46:31.634 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Checksum=-655926050
2025-08-22 10:46:31.634 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Checksum=-1155816943
2025-08-22 10:46:31.635 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Checksum=-741616458
2025-08-22 10:46:31.635 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Checksum=247496101
2025-08-22 10:46:31.635 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 12: Rank=12, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Checksum=-1552878247
2025-08-22 10:46:31.635 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 13: Rank=13, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Checksum=-753437310
2025-08-22 10:46:31.635 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 14: Rank=14, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Checksum=1463433185
2025-08-22 10:46:31.636 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Checksum=901666922
2025-08-22 10:46:31.636 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 16: Rank=16, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Checksum=-1022570356
2025-08-22 10:46:31.636 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 17: Rank=17, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Checksum=1520261797
2025-08-22 10:46:31.636 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 18: Rank=18, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Checksum=-63819014
2025-08-22 10:46:31.636 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始执行批量插入操作到schema [aaaaa_scenex]
2025-08-22 10:46:31.640 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 向schema [aaaaa_scenex] 成功插入了 18 条flyway_schema_history记录
2025-08-22 10:46:31.641 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema aaaaa_scenex 的flyway_schema_history表重新排序完成
2025-08-22 10:46:31.642 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema aaaaa_scenex 处理完成
2025-08-22 10:46:31.643 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始处理Schema: bzmmx_scenex
2025-08-22 10:46:31.643 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 没有数据映射、删除版本或添加版本需要处理
2025-08-22 10:46:31.667 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema bzmmx_scenex 中没有找到类型为DELETE的记录
2025-08-22 10:46:31.668 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始重新排序Schema bzmmx_scenex 的flyway_schema_history表
2025-08-22 10:46:31.668 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 查询schema [bzmmx_scenex] 的flyway_schema_history表，SQL: SELECT installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success FROM bzmmx_scenex.flyway_schema_history ORDER BY installed_rank
2025-08-22 10:46:31.671 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.671 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.671 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Type=SQL
2025-08-22 10:46:31.671 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Type=SQL
2025-08-22 10:46:31.671 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Type=SQL
2025-08-22 10:46:31.672 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Type=SQL
2025-08-22 10:46:31.672 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Type=SQL
2025-08-22 10:46:31.672 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Type=SQL
2025-08-22 10:46:31.672 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Type=SQL
2025-08-22 10:46:31.672 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Type=SQL
2025-08-22 10:46:31.673 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Type=SQL
2025-08-22 10:46:31.673 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 12: Rank=12, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Type=SQL
2025-08-22 10:46:31.673 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 13: Rank=13, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Type=SQL
2025-08-22 10:46:31.673 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 14: Rank=14, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Type=SQL
2025-08-22 10:46:31.673 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Type=SQL
2025-08-22 10:46:31.674 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 16: Rank=16, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Type=SQL
2025-08-22 10:46:31.674 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 17: Rank=17, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Type=SQL
2025-08-22 10:46:31.675 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 18: Rank=18, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Type=SQL
2025-08-22 10:46:31.675 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 从schema [bzmmx_scenex] 成功查询到 18 条flyway_schema_history记录
2025-08-22 10:46:31.675 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema bzmmx_scenex 中共有 18 条记录需要重新排序
2025-08-22 10:46:31.677 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 删除了Schema bzmmx_scenex 中的 18 条flyway_schema_history记录
2025-08-22 10:46:31.677 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 准备批量插入 18 条记录，SQL: INSERT INTO bzmmx_scenex.flyway_schema_history (installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-08-22 10:46:31.679 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=746558939
2025-08-22 10:46:31.679 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=734435211
2025-08-22 10:46:31.679 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Checksum=-85342651
2025-08-22 10:46:31.680 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Checksum=1766918736
2025-08-22 10:46:31.680 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Checksum=-164998370
2025-08-22 10:46:31.680 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Checksum=257700874
2025-08-22 10:46:31.680 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Checksum=-1713726568
2025-08-22 10:46:31.681 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Checksum=-655926050
2025-08-22 10:46:31.681 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Checksum=-1155816943
2025-08-22 10:46:31.681 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Checksum=-741616458
2025-08-22 10:46:31.681 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Checksum=247496101
2025-08-22 10:46:31.682 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 12: Rank=12, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Checksum=-1552878247
2025-08-22 10:46:31.682 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 13: Rank=13, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Checksum=-753437310
2025-08-22 10:46:31.683 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 14: Rank=14, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Checksum=1463433185
2025-08-22 10:46:31.683 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Checksum=901666922
2025-08-22 10:46:31.683 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 16: Rank=16, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Checksum=-1022570356
2025-08-22 10:46:31.683 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 17: Rank=17, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Checksum=1520261797
2025-08-22 10:46:31.684 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 18: Rank=18, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Checksum=-63819014
2025-08-22 10:46:31.684 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始执行批量插入操作到schema [bzmmx_scenex]
2025-08-22 10:46:31.685 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 向schema [bzmmx_scenex] 成功插入了 18 条flyway_schema_history记录
2025-08-22 10:46:31.687 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema bzmmx_scenex 的flyway_schema_history表重新排序完成
2025-08-22 10:46:31.688 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema bzmmx_scenex 处理完成
2025-08-22 10:46:31.688 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始处理Schema: project_scenex
2025-08-22 10:46:31.688 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 没有数据映射、删除版本或添加版本需要处理
2025-08-22 10:46:31.715 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema project_scenex 中没有找到类型为DELETE的记录
2025-08-22 10:46:31.715 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始重新排序Schema project_scenex 的flyway_schema_history表
2025-08-22 10:46:31.715 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 查询schema [project_scenex] 的flyway_schema_history表，SQL: SELECT installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success FROM project_scenex.flyway_schema_history ORDER BY installed_rank
2025-08-22 10:46:31.719 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.719 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.719 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Type=SQL
2025-08-22 10:46:31.719 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Type=SQL
2025-08-22 10:46:31.719 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Type=SQL
2025-08-22 10:46:31.719 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Type=SQL
2025-08-22 10:46:31.720 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Type=SQL
2025-08-22 10:46:31.720 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Type=SQL
2025-08-22 10:46:31.720 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Type=SQL
2025-08-22 10:46:31.720 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Type=SQL
2025-08-22 10:46:31.720 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Type=SQL
2025-08-22 10:46:31.720 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 12: Rank=12, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Type=SQL
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 13: Rank=13, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Type=SQL
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 14: Rank=14, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Type=SQL
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Type=SQL
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 16: Rank=16, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Type=SQL
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 17: Rank=17, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Type=SQL
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 18: Rank=18, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Type=SQL
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 从schema [project_scenex] 成功查询到 18 条flyway_schema_history记录
2025-08-22 10:46:31.721 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema project_scenex 中共有 18 条记录需要重新排序
2025-08-22 10:46:31.724 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 删除了Schema project_scenex 中的 18 条flyway_schema_history记录
2025-08-22 10:46:31.724 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 准备批量插入 18 条记录，SQL: INSERT INTO project_scenex.flyway_schema_history (installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-08-22 10:46:31.726 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=746558939
2025-08-22 10:46:31.726 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=734435211
2025-08-22 10:46:31.726 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Checksum=-85342651
2025-08-22 10:46:31.726 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Checksum=1766918736
2025-08-22 10:46:31.727 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Checksum=-164998370
2025-08-22 10:46:31.727 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Checksum=257700874
2025-08-22 10:46:31.728 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Checksum=-1713726568
2025-08-22 10:46:31.728 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Checksum=-655926050
2025-08-22 10:46:31.728 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Checksum=-1155816943
2025-08-22 10:46:31.728 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Checksum=-741616458
2025-08-22 10:46:31.728 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Checksum=247496101
2025-08-22 10:46:31.729 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 12: Rank=12, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Checksum=-1552878247
2025-08-22 10:46:31.729 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 13: Rank=13, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Checksum=-753437310
2025-08-22 10:46:31.729 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 14: Rank=14, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Checksum=1463433185
2025-08-22 10:46:31.729 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Checksum=901666922
2025-08-22 10:46:31.729 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 16: Rank=16, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Checksum=-1022570356
2025-08-22 10:46:31.730 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 17: Rank=17, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Checksum=1520261797
2025-08-22 10:46:31.730 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 18: Rank=18, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Checksum=-63819014
2025-08-22 10:46:31.730 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始执行批量插入操作到schema [project_scenex]
2025-08-22 10:46:31.732 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 向schema [project_scenex] 成功插入了 18 条flyway_schema_history记录
2025-08-22 10:46:31.734 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema project_scenex 的flyway_schema_history表重新排序完成
2025-08-22 10:46:31.735 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema project_scenex 处理完成
2025-08-22 10:46:31.735 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始处理Schema: scenex
2025-08-22 10:46:31.735 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 没有数据映射、删除版本或添加版本需要处理
2025-08-22 10:46:31.768 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema scenex 中没有找到类型为DELETE的记录
2025-08-22 10:46:31.768 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始重新排序Schema scenex 的flyway_schema_history表
2025-08-22 10:46:31.768 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 查询schema [scenex] 的flyway_schema_history表，SQL: SELECT installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success FROM scenex.flyway_schema_history ORDER BY installed_rank
2025-08-22 10:46:31.772 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.773 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.773 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Type=SQL
2025-08-22 10:46:31.774 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Type=SQL
2025-08-22 10:46:31.774 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Type=SQL
2025-08-22 10:46:31.774 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Type=SQL
2025-08-22 10:46:31.774 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Type=SQL
2025-08-22 10:46:31.774 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Type=SQL
2025-08-22 10:46:31.774 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Type=SQL
2025-08-22 10:46:31.774 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Type=SQL
2025-08-22 10:46:31.775 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Type=SQL
2025-08-22 10:46:31.775 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 12: Rank=12, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Type=SQL
2025-08-22 10:46:31.775 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 13: Rank=13, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Type=SQL
2025-08-22 10:46:31.775 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 14: Rank=14, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Type=SQL
2025-08-22 10:46:31.775 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Type=SQL
2025-08-22 10:46:31.775 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 16: Rank=16, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Type=SQL
2025-08-22 10:46:31.775 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 17: Rank=17, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Type=SQL
2025-08-22 10:46:31.776 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 18: Rank=18, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Type=SQL
2025-08-22 10:46:31.776 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 从schema [scenex] 成功查询到 18 条flyway_schema_history记录
2025-08-22 10:46:31.776 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema scenex 中共有 18 条记录需要重新排序
2025-08-22 10:46:31.779 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 删除了Schema scenex 中的 18 条flyway_schema_history记录
2025-08-22 10:46:31.779 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 准备批量插入 18 条记录，SQL: INSERT INTO scenex.flyway_schema_history (installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-08-22 10:46:31.782 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=746558939
2025-08-22 10:46:31.782 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=734435211
2025-08-22 10:46:31.783 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Checksum=-85342651
2025-08-22 10:46:31.783 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Checksum=1766918736
2025-08-22 10:46:31.783 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Checksum=-164998370
2025-08-22 10:46:31.783 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Checksum=257700874
2025-08-22 10:46:31.783 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Checksum=-1713726568
2025-08-22 10:46:31.783 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Checksum=-655926050
2025-08-22 10:46:31.784 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Checksum=-1155816943
2025-08-22 10:46:31.784 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Checksum=-741616458
2025-08-22 10:46:31.784 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Checksum=247496101
2025-08-22 10:46:31.784 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 12: Rank=12, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Checksum=-1552878247
2025-08-22 10:46:31.784 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 13: Rank=13, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Checksum=-753437310
2025-08-22 10:46:31.784 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 14: Rank=14, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Checksum=1463433185
2025-08-22 10:46:31.785 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Checksum=901666922
2025-08-22 10:46:31.785 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 16: Rank=16, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Checksum=-1022570356
2025-08-22 10:46:31.785 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 17: Rank=17, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Checksum=1520261797
2025-08-22 10:46:31.785 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 18: Rank=18, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Checksum=-63819014
2025-08-22 10:46:31.785 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始执行批量插入操作到schema [scenex]
2025-08-22 10:46:31.788 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 向schema [scenex] 成功插入了 18 条flyway_schema_history记录
2025-08-22 10:46:31.790 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema scenex 的flyway_schema_history表重新排序完成
2025-08-22 10:46:31.791 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema scenex 处理完成
2025-08-22 10:46:31.791 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始处理Schema: xqjii_scenex
2025-08-22 10:46:31.791 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 没有数据映射、删除版本或添加版本需要处理
2025-08-22 10:46:31.820 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema xqjii_scenex 中没有找到类型为DELETE的记录
2025-08-22 10:46:31.820 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始重新排序Schema xqjii_scenex 的flyway_schema_history表
2025-08-22 10:46:31.821 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 查询schema [xqjii_scenex] 的flyway_schema_history表，SQL: SELECT installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success FROM xqjii_scenex.flyway_schema_history ORDER BY installed_rank
2025-08-22 10:46:31.825 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.825 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Type=SQL
2025-08-22 10:46:31.825 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Type=SQL
2025-08-22 10:46:31.825 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Type=SQL
2025-08-22 10:46:31.825 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Type=SQL
2025-08-22 10:46:31.825 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Type=SQL
2025-08-22 10:46:31.826 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Type=SQL
2025-08-22 10:46:31.826 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Type=SQL
2025-08-22 10:46:31.826 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Type=SQL
2025-08-22 10:46:31.826 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Type=SQL
2025-08-22 10:46:31.826 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Type=SQL
2025-08-22 10:46:31.826 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 12: Rank=12, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Type=SQL
2025-08-22 10:46:31.826 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 13: Rank=13, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Type=SQL
2025-08-22 10:46:31.827 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 14: Rank=14, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Type=SQL
2025-08-22 10:46:31.827 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Type=SQL
2025-08-22 10:46:31.827 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 16: Rank=16, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Type=SQL
2025-08-22 10:46:31.827 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 17: Rank=17, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Type=SQL
2025-08-22 10:46:31.827 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 读取记录 18: Rank=18, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Type=SQL
2025-08-22 10:46:31.827 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 从schema [xqjii_scenex] 成功查询到 18 条flyway_schema_history记录
2025-08-22 10:46:31.827 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema xqjii_scenex 中共有 18 条记录需要重新排序
2025-08-22 10:46:31.832 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 删除了Schema xqjii_scenex 中的 18 条flyway_schema_history记录
2025-08-22 10:46:31.832 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 准备批量插入 18 条记录，SQL: INSERT INTO xqjii_scenex.flyway_schema_history (installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-08-22 10:46:31.834 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 1: Rank=1, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=746558939
2025-08-22 10:46:31.835 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 2: Rank=2, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__structure-init.sql, Checksum=734435211
2025-08-22 10:46:31.835 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 3: Rank=3, Version=2023.***********.30, Script=1.0.0/V2023.***********.30__data-init.sql, Checksum=-85342651
2025-08-22 10:46:31.835 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 4: Rank=4, Version=2023.***********.07, Script=1.0.0/V2023.***********.07__structure-init.sql, Checksum=1766918736
2025-08-22 10:46:31.835 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 5: Rank=5, Version=2023.***********.50, Script=1.0.0/V2023.***********.50__structure-init.sql, Checksum=-164998370
2025-08-22 10:46:31.835 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 6: Rank=6, Version=2023.***********.51, Script=1.0.0/V2023.***********.51__data-init.sql, Checksum=257700874
2025-08-22 10:46:31.835 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 7: Rank=7, Version=2023.***********.28, Script=1.0.0/V2023.***********.28__structure-init.sql, Checksum=-1713726568
2025-08-22 10:46:31.836 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 8: Rank=8, Version=2023.***********.39, Script=1.0.0/V2023.***********.39__data-init.sql, Checksum=-655926050
2025-08-22 10:46:31.836 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 9: Rank=9, Version=2023.***********.06, Script=V2023.***********.06__stricture-init.sql, Checksum=-1155816943
2025-08-22 10:46:31.836 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 10: Rank=10, Version=2023.***********.55, Script=1.0.0/V2023.***********.55__data-init.sql, Checksum=-741616458
2025-08-22 10:46:31.836 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 11: Rank=11, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__模版增加字段.sql, Checksum=247496101
2025-08-22 10:46:31.836 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 12: Rank=12, Version=2025.***********.11, Script=1.0.0/V2025.***********.11__新增菜单孪生体表.sql, Checksum=-1552878247
2025-08-22 10:46:31.836 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 13: Rank=13, Version=2025.***********, Script=1.0.0/V2025.***********__新增效果包是否默认字段.sql, Checksum=-753437310
2025-08-22 10:46:31.836 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 14: Rank=14, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加映射气泡逻辑.sql, Checksum=1463433185
2025-08-22 10:46:31.837 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 15: Rank=15, Version=2025.***********, Script=1.0.0/V2025.***********__映射模型增加气泡名称.sql, Checksum=901666922
2025-08-22 10:46:31.837 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 16: Rank=16, Version=2025.***********.07, Script=1.0.0/V2025.***********.07__修改数据权限字段类型.sql, Checksum=-1022570356
2025-08-22 10:46:31.837 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 17: Rank=17, Version=2025.***********.24, Script=1.0.0/V2025.***********.24__新增图例孪生体表.sql, Checksum=1520261797
2025-08-22 10:46:31.838 [SwingWorker-pool-1-thread-1] DEBUG com.uino.x.migration.service.DatabaseService - 添加到批处理 18: Rank=18, Version=2025.***********.56, Script=1.0.0/V2025.***********.56__新增场景校准位置字段.sql, Checksum=-63819014
2025-08-22 10:46:31.838 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 开始执行批量插入操作到schema [xqjii_scenex]
2025-08-22 10:46:31.840 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - 向schema [xqjii_scenex] 成功插入了 18 条flyway_schema_history记录
2025-08-22 10:46:31.842 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema xqjii_scenex 的flyway_schema_history表重新排序完成
2025-08-22 10:46:31.842 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Schema xqjii_scenex 处理完成
2025-08-22 10:46:31.843 [SwingWorker-pool-1-thread-1] INFO  com.uino.x.migration.service.DatabaseService - Flyway校验修复完成，共处理 5 个Schema
2025-08-22 11:08:34.435 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.438 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-22 11:08:34.622 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.627 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 11:08:34.769 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.769 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 11:08:34.809 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-22 11:08:34.819 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - Created 9 default transformation strategies
2025-08-22 11:08:34.863 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 9 transformation strategies in engine [MySQL-to-达梦]: [ShowCreateTransformationStrategy, CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, BacktickQuoteTransformationStrategy]
2025-08-22 11:08:34.863 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 9 strategies
2025-08-22 11:08:34.863 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-22 11:08:34.863 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-22 11:08:34.885 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.918 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 11:08:34.950 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.951 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 11:08:34.951 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 11:08:34.953 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.953 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 11:08:34.954 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.955 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 11:08:34.957 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.958 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 11:08:34.959 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.960 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 11:08:34.971 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.973 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:34.981 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 11:08:35.369 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-22 11:08:37.645 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 11:08:37.646 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.604 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.631 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.633 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 11:08:39.634 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.635 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.636 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-22 11:08:39.636 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 11:08:39.636 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 11:08:39.643 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.643 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 11:08:39.644 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.645 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 11:08:39.645 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.646 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 11:08:39.647 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.647 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 11:08:39.655 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.656 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:08:39.663 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 11:08:39.663 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
2025-08-22 11:11:36.341 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.344 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-22 11:11:36.535 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.539 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 11:11:36.652 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.653 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 11:11:36.697 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-22 11:11:36.706 [AWT-EventQueue-0] ERROR com.uino.x.migration.ui.SqlTransformationPanel - 更新转换引擎失败
java.lang.UnsupportedOperationException: null
	at java.base/java.util.AbstractList.add(AbstractList.java:153)
	at java.base/java.util.AbstractList.add(AbstractList.java:111)
	at java.base/java.util.AbstractCollection.addAll(AbstractCollection.java:336)
	at com.uino.x.migration.ui.SqlTransformationPanel.updateTransformationEngine(SqlTransformationPanel.java:261)
	at com.uino.x.migration.ui.SqlTransformationPanel.initializeTransformationEngine(SqlTransformationPanel.java:238)
	at com.uino.x.migration.ui.SqlTransformationPanel.<init>(SqlTransformationPanel.java:48)
	at com.uino.x.migration.ui.MainFrame.initializeComponents(MainFrame.java:91)
	at com.uino.x.migration.ui.MainFrame.<init>(MainFrame.java:59)
	at com.uino.x.migration.DatabaseMigrationToolApplication.lambda$0(DatabaseMigrationToolApplication.java:39)
	at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:742)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
2025-08-22 11:11:36.709 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-22 11:11:36.728 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.761 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 11:11:36.800 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.802 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 11:11:36.803 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 11:11:36.805 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.805 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 11:11:36.806 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.807 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 11:11:36.810 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.811 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 11:11:36.813 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.813 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 11:11:36.826 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.828 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:11:36.837 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 11:11:37.238 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-22 11:11:57.742 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 11:11:57.743 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.083 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.085 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-22 11:12:55.333 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.339 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 11:12:55.485 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.486 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 11:12:55.539 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-22 11:12:55.583 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-22 11:12:55.583 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-22 11:12:55.583 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-22 11:12:55.583 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-22 11:12:55.604 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.631 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 11:12:55.672 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.673 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 11:12:55.674 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 11:12:55.675 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.676 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 11:12:55.677 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.678 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 11:12:55.681 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.682 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 11:12:55.684 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.685 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 11:12:55.701 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.703 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:55.712 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 11:12:56.143 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-22 11:12:57.855 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 11:12:57.856 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 11:12:59.814 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation statistics: 
SqlTransformationEngine [MySQL-to-达梦] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 10 registered
  Memory: Estimated Size=0 bytes
2025-08-22 11:12:59.903 [AWT-EventQueue-0] WARN  c.u.x.c.s.t.s.MergeIntoTransformationStrategy - Failed to retrieve column metadata for table: twin_class -> The current spring environment is unavailable
2025-08-22 11:12:59.980 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [REPLACE INTO `twin_class`(`id`, `name`, `code`, `group_id`, `level`, `data_type`, `custom_color`, `bubble_info_id`, `things_model_id`, `things_model_uuid`, `status`, `form`, `structure`, `pure`, `create_user`) VALUES (1486428435602665473, 'XXV菜单', 'MENU', 1476825472376508418, 'OTHER', 'FORM', NULL, NULL, NULL, NULL, 0, '{\"list\":[{\"type\":\"input\",\"label\":\"孪生体编码\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"UniqueCode\",\"key\":\"input_1635917827146\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\"}]},{\"type\":\"input\",\"label\":\"视角\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"view\",\"key\":\"input_1676605829257\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\",\"type\":\"string\"}]}],\"config\":{\"layout\":\"horizontal\",\"labelCol\":{\"xs\":4,\"sm\":4,\"md\":4,\"lg\":4,\"xl\":4,\"xxl\":4},\"labelWidth\":100,\"labelLayout\":\"flex\",\"wrapperCol\":{\"xs\":18,\"sm\":18,\"md\":18,\"lg\":18,\"xl\":18,\"xxl\":18},\"hideRequiredMark\":false,\"customStyle\":\"\"}}', '{\"table\":\"twin_menu\",\"column\":[{\"code\":\"9d3b4c28dfea84b76922211e7b75963f\",\"comment\":\"唯一标识\",\"name\":\"uuid\",\"required\":\"not null\",\"type\":\"bigint primary key\",\"unique\":true},{\"code\":\"input_1635917827146\",\"comment\":\"孪生体编码\",\"length\":255,\"name\":\"UniqueCode\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"input_1676605829257\",\"comment\":\"视角\",\"length\":255,\"name\":\"view\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1ee9d83c592de78123b4dcdcfa0c1a80\",\"comment\":\"创建时间\",\"defaultValue\":\"current_timestamp\",\"name\":\"create_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"fa1506f5f75d60eb2bc62ce19ad17121\",\"comment\":\"创建人\",\"length\":127,\"name\":\"create_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1a8422f18b26369c37923b24b9a20c7d\",\"comment\":\"更新时间\",\"defaultValue\":\"current_timestamp on update current_timestamp\",\"name\":\"update_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"2a429205c9d6b3600745529d0e978eb8\",\"comment\":\"更新人\",\"length\":127,\"name\":\"update_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false}],\"@version\":1,\"@session\":\"1626516592439603201\"}', 0, 1557912667941769217);]
Result:   [MERGE INTO "twin_class" t USING (SELECT 1486428435602665473 AS id, 'XXV菜单' AS name, 'MENU' AS code, 1476825472376508418 AS group_id, 'OTHER' AS level, 'FORM' AS data_type, NULL AS custom_color, NULL AS bubble_info_id, NULL AS things_model_id, NULL AS things_model_uuid, 0 AS status, '{\"list\":[{\"type\":\"input\",\"label\":\"孪生体编码\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"UniqueCode\",\"key\":\"input_1635917827146\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\"}]},{\"type\":\"input\",\"label\":\"视角\",\"options\":{\"type\":\"text\",\"width\":\"100%\",\"defaultValue\":\"\",\"placeholder\":\"请输入\",\"clearable\":false,\"maxLength\":255,\"addonBefore\":\"\",\"addonAfter\":\"\",\"unique\":false,\"hidden\":false,\"disabled\":false},\"model\":\"view\",\"key\":\"input_1676605829257\",\"help\":\"\",\"rules\":[{\"required\":false,\"message\":\"必填项\",\"type\":\"string\"}]}],\"config\":{\"layout\":\"horizontal\",\"labelCol\":{\"xs\":4,\"sm\":4,\"md\":4,\"lg\":4,\"xl\":4,\"xxl\":4},\"labelWidth\":100,\"labelLayout\":\"flex\",\"wrapperCol\":{\"xs\":18,\"sm\":18,\"md\":18,\"lg\":18,\"xl\":18,\"xxl\":18},\"hideRequiredMark\":false,\"customStyle\":\"\"}}' AS form, '{\"table\":\"twin_menu\",\"column\":[{\"code\":\"9d3b4c28dfea84b76922211e7b75963f\",\"comment\":\"唯一标识\",\"name\":\"uuid\",\"required\":\"not null\",\"type\":\"bigint primary key\",\"unique\":true},{\"code\":\"input_1635917827146\",\"comment\":\"孪生体编码\",\"length\":255,\"name\":\"UniqueCode\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"input_1676605829257\",\"comment\":\"视角\",\"length\":255,\"name\":\"view\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1ee9d83c592de78123b4dcdcfa0c1a80\",\"comment\":\"创建时间\",\"defaultValue\":\"current_timestamp\",\"name\":\"create_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"fa1506f5f75d60eb2bc62ce19ad17121\",\"comment\":\"创建人\",\"length\":127,\"name\":\"create_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false},{\"code\":\"1a8422f18b26369c37923b24b9a20c7d\",\"comment\":\"更新时间\",\"defaultValue\":\"current_timestamp on update current_timestamp\",\"name\":\"update_time\",\"required\":\"null\",\"type\":\"datetime\",\"unique\":false},{\"code\":\"2a429205c9d6b3600745529d0e978eb8\",\"comment\":\"更新人\",\"length\":127,\"name\":\"update_user\",\"required\":\"null\",\"type\":\"varchar\",\"unique\":false}],\"@version\":1,\"@session\":\"1626516592439603201\"}' AS structure, 0 AS pure, 1557912667941769217 AS create_user FROM DUAL) s ON (t.id = s.id) WHEN MATCHED THEN UPDATE SET t.name = s.name, t.code = s.code, t.group_id = s.group_id, t.level = s.level, t.data_type = s.data_type, t.custom_color = s.custom_color, t.bubble_info_id = s.bubble_info_id, t.things_model_id = s.things_model_id, t.things_model_uuid = s.things_model_uuid, t.status = s.status, t.form = s.form, t.structure = s.structure, t.pure = s.pure, t.create_user = s.create_user WHEN NOT MATCHED THEN INSERT (id, name, code, group_id, level, data_type, custom_color, bubble_info_id, things_model_id, things_model_uuid, status, form, structure, pure, create_user) VALUES (s.id, s.name, s.code, s.group_id, s.level, s.data_type, s.custom_color, s.bubble_info_id, s.things_model_id, s.things_model_uuid, s.status, s.form, s.structure, s.pure, s.create_user);]
2025-08-22 11:12:59.982 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 2749, 转换后SQL长度: 3558
2025-08-22 11:13:08.142 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [ALTER TABLE `twin_class_model_mapping`
    ADD COLUMN `bubble_info_id` bigint(0) NULL COMMENT '气泡ID';]
Result:   [ALTER TABLE "twin_class_model_mapping" ADD COLUMN "bubble_info_id" bigint NULL;;
COMMENT ON COLUMN "twin_class_model_mapping"."bubble_info_id" IS '气泡ID';]
2025-08-22 11:13:08.142 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 101, 转换后SQL长度: 153
2025-08-22 14:30:51.933 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:51.935 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-22 14:30:52.075 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.079 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-22 14:30:52.197 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.198 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 14:30:52.247 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-22 14:30:52.282 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-22 14:30:52.282 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-22 14:30:52.282 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-22 14:30:52.283 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-22 14:30:52.298 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.334 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 14:30:52.369 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.370 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-22 14:30:52.371 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-22 14:30:52.372 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.372 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-22 14:30:52.373 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.374 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 2 个配置
2025-08-22 14:30:52.376 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.376 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-22 14:30:52.378 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.378 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-22 14:30:52.389 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.390 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:30:52.397 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-22 14:30:54.289 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-22 14:30:58.607 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 14:30:58.608 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-22 14:31:03.050 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-22 14:31:03.051 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
